#!/usr/bin/env python3
"""
Script to sort 002.csv files in Product-use folder by time column
"""

import pandas as pd
import os
from datetime import datetime

def sort_csv_by_time(file_path):
    """按time列对CSV文件进行排序"""
    print(f"\n处理文件: {file_path}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False
    
    try:
        # 读取CSV文件
        df = pd.read_csv(file_path)
        print(f"原始数据行数: {len(df)}")
        print(f"列名: {list(df.columns)}")
        
        # 检查是否有time列
        if 'time' not in df.columns:
            print("错误: 未找到 'time' 列")
            return False
        
        # 转换time为datetime格式
        print("转换time为datetime格式...")
        df['time_dt'] = pd.to_datetime(df['time'])
        
        # 显示排序前的时间范围
        print(f"时间范围: {df['time_dt'].min()} 到 {df['time_dt'].max()}")
        
        # 检查是否已经按时间排序
        is_sorted = df['time_dt'].is_monotonic_increasing
        print(f"排序前是否已按时间排序: {is_sorted}")
        
        # 显示排序前的前几行和后几行时间
        print("排序前前5行time:")
        print(df['time'].head())
        print("排序前后5行time:")
        print(df['time'].tail())
        
        # 按time_dt排序
        print("按time排序...")
        df_sorted = df.sort_values('time_dt').reset_index(drop=True)
        
        # 删除临时的datetime列
        df_sorted = df_sorted.drop('time_dt', axis=1)
        
        # 验证排序后是否正确
        df_sorted['time_dt'] = pd.to_datetime(df_sorted['time'])
        is_sorted_after = df_sorted['time_dt'].is_monotonic_increasing
        print(f"排序后是否按时间排序: {is_sorted_after}")
        
        # 显示排序后的前几行和后几行时间
        print("排序后前5行time:")
        print(df_sorted['time'].head())
        print("排序后后5行time:")
        print(df_sorted['time'].tail())
        
        # 删除临时列
        df_sorted = df_sorted.drop('time_dt', axis=1)
        
        # 保存排序后的文件
        print(f"保存排序后的文件: {file_path}")
        df_sorted.to_csv(file_path, index=False)
        print(f"成功处理 {len(df_sorted)} 行数据")
        
        return True
        
    except Exception as e:
        print(f"处理文件时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始对Product-use文件夹下lianghe、yantang、yinhe文件夹下的002.csv文件按时间顺序进行排序...")
    
    # 定义要处理的文件列表
    files = [
        'Product-use/lianghe/002.csv',
        'Product-use/yantang/002.csv', 
        'Product-use/yinhe/002.csv'
    ]
    
    success_count = 0
    total_count = len(files)
    
    # 处理每个文件
    for file_path in files:
        if sort_csv_by_time(file_path):
            success_count += 1
        print("-" * 50)
    
    # 显示处理结果
    print(f"\n处理完成!")
    print(f"成功处理: {success_count}/{total_count} 个文件")
    
    if success_count == total_count:
        print("所有文件都已成功按时间顺序排序！")
    else:
        print(f"有 {total_count - success_count} 个文件处理失败")

if __name__ == "__main__":
    main()
