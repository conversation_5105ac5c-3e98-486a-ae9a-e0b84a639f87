#!/usr/bin/env python3
"""
流量预测模型 - 使用NeuralForecast TimeXer模型
目标变量：流量(OT)
外生变量：雨量(tp)
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from neuralforecast import NeuralForecast
from neuralforecast.models import TimeXer
from neuralforecast.losses.pytorch import RMSE
import warnings
warnings.filterwarnings('ignore')

def calculate_nse(y_true, y_pred):
    """计算Nash-Sutcliffe效率系数"""
    if len(y_true) != len(y_pred):
        raise ValueError("y_true和y_pred长度必须相同")
    if denominator == 0:
        return np.nan
    numerator = np.sum((y_true - y_pred) ** 2)
    denominator = np.sum((y_true - np.mean(y_true)) ** 2)
    if denominator == 0:
        return np.nan  # 避免除以0的情况
    nse = 1 - (numerator / denominator)
    return nse

def calculate_correlation(y_true, y_pred):
    """计算相关系数"""
    return np.corrcoef(y_true, y_pred)[0, 1]

def calculate_rmse(y_true, y_pred):
    """计算RMSE"""
    return np.sqrt(np.mean((y_true - y_pred) ** 2))

def load_and_prepare_data(file_path):
    """加载和准备数据"""
    print("Loading data...")
    df = pd.read_csv(file_path)
    
    # 转换时间列
    df['date'] = pd.to_datetime(df['date'])
    
    # 重命名列以符合NeuralForecast格式
    df_prepared = df.rename(columns={
        'date': 'ds',
        'OT': 'y',
        'tp': 'rainfall'
    })
    
    # 添加unique_id列（NeuralForecast要求）
    df_prepared['unique_id'] = 'lianghe'
    
    # 重新排列列的顺序
    df_prepared = df_prepared[['unique_id', 'ds', 'y', 'rainfall']]
    
    print(f"Data shape: {df_prepared.shape}")
    print(f"Date range: {df_prepared['ds'].min()} to {df_prepared['ds'].max()}")
    print(f"Flow range: {df_prepared['y'].min():.3f} to {df_prepared['y'].max():.3f}")
    print(f"Rainfall range: {df_prepared['rainfall'].min():.3f} to {df_prepared['rainfall'].max():.3f}")
    
    return df_prepared

def split_data(df, train_ratio=0.8):
    """分割训练集和测试集"""
    n_total = len(df)
    n_train = int(n_total * train_ratio)
    
    train_df = df.iloc[:n_train].copy()
    test_df = df.iloc[n_train:].copy()
    
    print(f"Training data: {len(train_df)} samples")
    print(f"Testing data: {len(test_df)} samples")
    print(f"Training period: {train_df['ds'].min()} to {train_df['ds'].max()}")
    print(f"Testing period: {test_df['ds'].min()} to {test_df['ds'].max()}")
    
    return train_df, test_df

def train_timexer_model(train_df, input_size=128, horizon=12):
    """训练TimeXer模型"""
    print("Training TimeXer model...")

    # 获取时间序列数量
    n_series = len(train_df['unique_id'].unique())

    # 创建TimeXer模型
    models = [
        TimeXer(
            h=horizon,                    # 预测窗口
            input_size=input_size,        # 输入窗口大小
            n_series=n_series,            # 时间序列数量
            futr_exog_list=['rainfall'],  # 未来外生变量列表
            loss=RMSE(),                  # 损失函数
            max_steps=1000,               # 最大训练步数
            learning_rate=1e-3,           # 学习率
            batch_size=32,                # 批次大小
            windows_batch_size=256,       # 窗口批次大小
            random_seed=42,               # 随机种子
        )
    ]
    
    # 创建NeuralForecast对象
    nf = NeuralForecast(
        models=models,
        freq='H'  # 小时频率
    )
    
    # 训练模型 - 包含外生变量
    # 为训练准备外生变量数据
    train_futr_df = train_df[['unique_id', 'ds', 'rainfall']].copy()

    print(f"Training with exogenous variables...")
    print(f"Training data shape: {train_df.shape}")
    print(f"Future exogenous data shape: {train_futr_df.shape}")

    # 训练模型，包含未来外生变量
    nf.fit(train_df, futr_df=train_futr_df, static_df=None)
    
    print("Model training completed!")
    return nf

def make_predictions(nf, train_df, test_df, horizon=12):
    """进行预测"""
    print("Making predictions...")

    # 准备未来外生变量数据
    # TimeXer需要未来的外生变量数据进行预测
    futr_df = test_df[['unique_id', 'ds', 'rainfall']].copy()

    # 限制预测长度为horizon
    futr_df = futr_df.head(horizon)

    print(f"Future exogenous data shape: {futr_df.shape}")
    print(f"Predicting {len(futr_df)} time steps...")

    # 进行预测
    predictions = nf.predict(futr_df=futr_df)

    print("Predictions completed!")
    return predictions

def evaluate_model(test_df, predictions, horizon=12):
    """评估模型性能"""
    print("Evaluating model performance...")
    
    # 获取实际值和预测值
    y_true = test_df['y'].values[:len(predictions)]
    y_pred = predictions['TimeXer'].values
    
    # 计算评估指标
    rmse = calculate_rmse(y_true, y_pred)
    nse = calculate_nse(y_true, y_pred)
    correlation = calculate_correlation(y_true, y_pred)
    
    print(f"\nModel Evaluation Results:")
    print(f"RMSE: {rmse:.4f}")
    print(f"NSE: {nse:.4f}")
    print(f"Correlation (r): {correlation:.4f}")
    
    return {
        'rmse': rmse,
        'nse': nse,
        'correlation': correlation,
        'y_true': y_true,
        'y_pred': y_pred
    }

def plot_results(test_df, predictions, results, save_path=None):
    """绘制预测结果"""
    print("Plotting results...")
    
    plt.figure(figsize=(15, 8))
    
    # 获取时间序列
    test_dates = test_df['ds'].values[:len(predictions)]
    
    # 绘制实际值和预测值
    plt.plot(test_dates, results['y_true'], 'g-', linewidth=2, label='Actual Flow', alpha=0.8)
    plt.plot(test_dates, results['y_pred'], 'r-', linewidth=2, label='Predicted Flow', alpha=0.8)
    
    plt.xlabel('Time')
    plt.ylabel('Flow (m³/s)')
    plt.title(f'Flow Prediction Results - TimeXer Model\n'
              f'RMSE: {results["rmse"]:.4f}, NSE: {results["nse"]:.4f}, r: {results["correlation"]:.4f}')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Plot saved to: {save_path}")
    
    plt.show()

def main():
    """主函数"""
    print("=== Flow Prediction using TimeXer Model ===")
    
    # 参数设置
    file_path = 'Flow_Prediction/lianghe.csv'
    input_size = 128
    horizon = 12
    train_ratio = 0.8
    
    try:
        # 1. 加载和准备数据
        df = load_and_prepare_data(file_path)
        
        # 2. 分割数据
        train_df, test_df = split_data(df, train_ratio)
        
        # 3. 训练模型
        nf = train_timexer_model(train_df, input_size, horizon)
        
        # 4. 进行预测
        predictions = make_predictions(nf, train_df, test_df, horizon)
        
        # 5. 评估模型
        results = evaluate_model(test_df, predictions, horizon)
        
        # 6. 绘制结果
        plot_results(test_df, predictions, results, 'Flow_Prediction/timexer_prediction_results.png')
        
        # 7. 保存预测结果
        results_df = pd.DataFrame({
            'datetime': test_df['ds'].values[:len(predictions)],
            'actual_flow': results['y_true'],
            'predicted_flow': results['y_pred']
        })
        results_df.to_csv('Flow_Prediction/timexer_prediction_results.csv', index=False)
        print("Prediction results saved to: Flow_Prediction/timexer_prediction_results.csv")
        
        print("\n=== Prediction completed successfully! ===")
        
    except Exception as e:
        print(f"Error occurred: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
