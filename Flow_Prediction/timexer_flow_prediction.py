#!/usr/bin/env python3
"""
流量预测模型 - 使用NeuralForecast TimeXer模型
目标变量：流量(OT)
外生变量：雨量(tp)
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from neuralforecast import NeuralForecast
from neuralforecast.models import TimeXer
from neuralforecast.losses.pytorch import RMSE
import warnings
warnings.filterwarnings('ignore')

def calculate_nse(y_true, y_pred):
    """计算Nash-Sutcliffe效率系数"""
    if len(y_true) != len(y_pred):
        raise ValueError("y_true和y_pred长度必须相同")

    numerator = np.sum((y_true - y_pred) ** 2)
    denominator = np.sum((y_true - np.mean(y_true)) ** 2)

    if denominator == 0:
        return np.nan  # 避免除以0的情况

    nse = 1 - (numerator / denominator)
    return nse

def calculate_correlation(y_true, y_pred):
    """计算相关系数"""
    return np.corrcoef(y_true, y_pred)[0, 1]

def calculate_rmse(y_true, y_pred):
    """计算RMSE"""
    return np.sqrt(np.mean((y_true - y_pred) ** 2))

def load_and_prepare_data(file_path):
    """加载和准备数据"""
    print("Loading data...")
    df = pd.read_csv(file_path)
    
    # 转换时间列
    df['date'] = pd.to_datetime(df['date'])
    
    # 重命名列以符合NeuralForecast格式
    df_prepared = df.rename(columns={
        'date': 'ds',
        'OT': 'y',
        'tp': 'rainfall'
    })
    
    # 添加unique_id列（NeuralForecast要求）
    df_prepared['unique_id'] = 'lianghe'
    
    # 重新排列列的顺序
    df_prepared = df_prepared[['unique_id', 'ds', 'y', 'rainfall']]
    
    print(f"Data shape: {df_prepared.shape}")
    print(f"Date range: {df_prepared['ds'].min()} to {df_prepared['ds'].max()}")
    print(f"Flow range: {df_prepared['y'].min():.3f} to {df_prepared['y'].max():.3f}")
    print(f"Rainfall range: {df_prepared['rainfall'].min():.3f} to {df_prepared['rainfall'].max():.3f}")
    
    return df_prepared

def split_data(df, train_ratio=0.8):
    """分割训练集和测试集"""
    n_total = len(df)
    n_train = int(n_total * train_ratio)
    
    train_df = df.iloc[:n_train].copy()
    test_df = df.iloc[n_train:].copy()
    
    print(f"Training data: {len(train_df)} samples")
    print(f"Testing data: {len(test_df)} samples")
    print(f"Training period: {train_df['ds'].min()} to {train_df['ds'].max()}")
    print(f"Testing period: {test_df['ds'].min()} to {test_df['ds'].max()}")
    
    return train_df, test_df

def train_timexer_model(train_df, input_size=128, horizon=12):
    """训练TimeXer模型"""
    print("Training TimeXer model...")

    # 获取时间序列数量
    n_series = len(train_df['unique_id'].unique())

    # 创建TimeXer模型，使用均值方差标准化
    models = [
        TimeXer(
            h=horizon,                    # 预测窗口
            input_size=input_size,        # 输入窗口大小
            n_series=n_series,            # 时间序列数量
            futr_exog_list=['rainfall'],  # 未来外生变量列表
            loss=RMSE(),                  # 损失函数
            max_steps=1000,               # 最大训练步数
            learning_rate=1e-3,           # 学习率
            batch_size=16,                # 批次大小
            random_seed=42,               # 随机种子
            scaler_type='standard',       # 使用均值方差标准化
        )
    ]
    
    # 创建NeuralForecast对象
    nf = NeuralForecast(
        models=models,
        freq='H'  # 小时频率
    )
    
    # 训练模型 - 包含外生变量
    # 为训练准备外生变量数据
    train_futr_df = train_df[['unique_id', 'ds', 'rainfall']].copy()

    print(f"Training with exogenous variables...")
    print(f"Training data shape: {train_df.shape}")
    print(f"Future exogenous data shape: {train_futr_df.shape}")

    # 训练模型，包含未来外生变量
    nf.fit(train_df, static_df=None)
    
    print("Model training completed!")
    return nf

def make_simple_predictions(nf, train_df, test_df, horizon=12):
    """进行简单的一次性预测，然后在测试集上进行滑动窗口评估"""
    print("Making predictions with sliding window evaluation...")

    # 准备未来外生变量数据（整个测试集）
    futr_df = test_df[['unique_id', 'ds', 'rainfall']].copy()

    print(f"Future exogenous data shape: {futr_df.shape}")
    print(f"Predicting for test period...")

    # 进行预测
    predictions = nf.predict(futr_df=futr_df)

    print("Predictions completed!")
    print(f"Predictions shape: {predictions.shape}")

    # 进行滑动窗口评估
    print("Performing sliding window evaluation...")

    all_predictions = []
    all_actuals = []
    all_timestamps = []

    # 计算滑动窗口数量
    n_windows = len(test_df) - horizon + 1

    print(f"Number of evaluation windows: {n_windows}")

    for i in range(n_windows):
        # 获取当前窗口的预测值和实际值
        window_start = i
        window_end = i + horizon

        # 从预测结果中获取当前窗口的预测值
        pred_values = predictions.iloc[window_start:window_end]['TimeXer'].values
        actual_values = test_df.iloc[window_start:window_end]['y'].values
        timestamps = test_df.iloc[window_start:window_end]['ds'].values

        all_predictions.extend(pred_values)
        all_actuals.extend(actual_values)
        all_timestamps.extend(timestamps)

        if (i + 1) % 100 == 0:
            print(f"Completed {i + 1}/{n_windows} evaluation windows")

    print(f"Sliding window evaluation completed!")
    print(f"Total evaluation samples: {len(all_predictions)}")

    # 创建结果DataFrame
    results_df = pd.DataFrame({
        'ds': all_timestamps,
        'y_true': all_actuals,
        'TimeXer': all_predictions
    })

    return results_df

def evaluate_sliding_window_model(predictions_df):
    """评估滑动窗口预测模型性能"""
    print("Evaluating sliding window model performance...")

    # 从预测结果DataFrame中获取实际值和预测值
    y_true = predictions_df['y_true'].values
    y_pred = predictions_df['TimeXer'].values

    print(f"Total evaluation samples: {len(y_true)}")

    # 计算评估指标
    rmse = calculate_rmse(y_true, y_pred)
    nse = calculate_nse(y_true, y_pred)
    correlation = calculate_correlation(y_true, y_pred)

    print(f"\nSliding Window Model Evaluation Results:")
    print(f"RMSE: {rmse:.4f}")
    print(f"NSE: {nse:.4f}")
    print(f"Correlation (r): {correlation:.4f}")

    return {
        'rmse': rmse,
        'nse': nse,
        'correlation': correlation,
        'y_true': y_true,
        'y_pred': y_pred,
        'timestamps': predictions_df['ds'].values
    }

def plot_sliding_window_results(predictions_df, results, save_path=None):
    """绘制滑动窗口预测结果"""
    print("Plotting sliding window results...")

    plt.figure(figsize=(15, 8))

    # 获取时间序列
    timestamps = results['timestamps']

    # 绘制实际值和预测值
    plt.plot(timestamps, results['y_true'], 'g-', linewidth=2, label='Actual Flow', alpha=0.8)
    plt.plot(timestamps, results['y_pred'], 'r-', linewidth=2, label='Predicted Flow', alpha=0.8)

    plt.xlabel('Time')
    plt.ylabel('Flow (m³/s)')
    plt.title(f'Sliding Window Flow Prediction Results - TimeXer Model (Standard Scaler)\n'
              f'RMSE: {results["rmse"]:.4f}, NSE: {results["nse"]:.4f}, r: {results["correlation"]:.4f}')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Plot saved to: {save_path}")

    plt.show()

def main():
    """主函数"""
    print("=== Flow Prediction using TimeXer Model ===")
    
    # 参数设置
    file_path = 'Flow_Prediction/lianghe.csv'
    input_size = 128
    horizon = 12
    train_ratio = 0.8
    
    try:
        # 1. 加载和准备数据
        df = load_and_prepare_data(file_path)
        
        # 2. 分割数据
        train_df, test_df = split_data(df, train_ratio)
        
        # 3. 训练模型
        nf = train_timexer_model(train_df, input_size, horizon)
        
        # 4. 进行滑动窗口预测
        predictions = make_sliding_window_predictions(nf, train_df, test_df, horizon, input_size)
        
        # 5. 评估滑动窗口模型
        results = evaluate_sliding_window_model(predictions)
        
        # 6. 绘制滑动窗口结果
        plot_sliding_window_results(predictions, results, 'Flow_Prediction/timexer_sliding_window_results.png')
        
        # 7. 保存滑动窗口预测结果
        # 预测结果已经在predictions DataFrame中
        predictions.to_csv('Flow_Prediction/timexer_sliding_window_results.csv', index=False)
        print("Sliding window prediction results saved to: Flow_Prediction/timexer_sliding_window_results.csv")
        
        print("\n=== Prediction completed successfully! ===")
        
    except Exception as e:
        print(f"Error occurred: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
