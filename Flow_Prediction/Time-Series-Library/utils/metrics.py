import numpy as np


def RSE(pred, true):
    return np.sqrt(np.sum((true - pred) ** 2)) / np.sqrt(np.sum((true - true.mean()) ** 2))


def CORR(pred, true):
    u = ((true - true.mean(0)) * (pred - pred.mean(0))).sum(0)
    d = np.sqrt(((true - true.mean(0)) ** 2 * (pred - pred.mean(0)) ** 2).sum(0))
    return (u / d).mean(-1)


def MAE(pred, true):
    return np.mean(np.abs(true - pred))


def MSE(pred, true):
    return np.mean((true - pred) ** 2)


def RMSE(pred, true):
    return np.sqrt(MSE(pred, true))


def MAPE(pred, true):
    return np.mean(np.abs((true - pred) / true))


def MSPE(pred, true):
    return np.mean(np.square((true - pred) / true))


# def metric(pred, true):
#     mae = MAE(pred, true)
#     mse = MSE(pred, true)
#     rmse = RMSE(pred, true)
#     mape = MAPE(pred, true)
#     mspe = MSPE(pred, true)

#     return mae, mse, rmse, mape, mspe


def NSE(pred, true):
    numerator = np.sum((true - pred)**2)
    denominator = np.sum((true - np.mean(true))**2)
    return 1 - (numerator / denominator)

def r(pred, true):
    u = ((true - true.mean()) * (pred - pred.mean())).sum()
    d = np.sqrt(((true - true.mean()) ** 2).sum() * ((pred - pred.mean()) ** 2).sum())
    return u / d


def metric(pred, true):
    rmse = RMSE(pred, true)
    nse = NSE(pred, true)   
    corr = r(pred, true)

    return rmse, nse, corr