#!/bin/bash

# TimeXer模型使用Soft-DTW损失函数训练脚本
# 作者：AI Assistant
# 日期：2025-06-18

echo "=========================================="
echo "TimeXer with Soft-DTW Loss Training Script"
echo "=========================================="

# 激活conda环境
source ~/anaconda3/etc/profile.d/conda.sh
conda activate flood

# 检查环境
echo "检查Python环境..."
python --version
echo "检查PyTorch..."
python -c "import torch; print(f'PyTorch version: {torch.__version__}')"

# 设置基本参数
MODEL="TimeXer"
TASK="long_term_forecast"
FEATURES="MS"
SEQ_LEN=168
LABEL_LEN=48
BATCH_SIZE=32
LEARNING_RATE=0.0001
TRAIN_EPOCHS=10

echo "基本参数设置完成"
echo "模型: $MODEL"
echo "任务: $TASK"
echo "序列长度: $SEQ_LEN"
echo "预测长度: 将在下面的实验中变化"

# 实验1: 使用传统RMSE损失函数（基准）
echo ""
echo "=========================================="
echo "实验1: 基准实验 - RMSE损失函数"
echo "=========================================="

for DATASET in "lianghe" "yantang" "yinhe"; do
    for PRED_LEN in 24 48 72; do
        echo "训练 $DATASET 数据集，预测长度 $PRED_LEN 小时 - RMSE损失"
        
        python run.py \
            --task_name $TASK \
            --is_training 1 \
            --model_id "${DATASET}_${PRED_LEN}h_rmse" \
            --model $MODEL \
            --data $DATASET \
            --features $FEATURES \
            --seq_len $SEQ_LEN \
            --label_len $LABEL_LEN \
            --pred_len $PRED_LEN \
            --loss RMSE \
            --train_epochs $TRAIN_EPOCHS \
            --batch_size $BATCH_SIZE \
            --learning_rate $LEARNING_RATE \
            --des "RMSE_baseline"
        
        echo "完成 $DATASET $PRED_LEN h RMSE训练"
    done
done

# 实验2: 使用Soft-DTW损失函数
echo ""
echo "=========================================="
echo "实验2: Soft-DTW损失函数"
echo "=========================================="

for DATASET in "lianghe" "yantang" "yinhe"; do
    for PRED_LEN in 24 48 72; do
        for GAMMA in 0.5 1.0 2.0; do
            echo "训练 $DATASET 数据集，预测长度 $PRED_LEN 小时 - Soft-DTW损失 (γ=$GAMMA)"
            
            python run.py \
                --task_name $TASK \
                --is_training 1 \
                --model_id "${DATASET}_${PRED_LEN}h_softdtw_g${GAMMA}" \
                --model $MODEL \
                --data $DATASET \
                --features $FEATURES \
                --seq_len $SEQ_LEN \
                --label_len $LABEL_LEN \
                --pred_len $PRED_LEN \
                --loss SoftDTW \
                --soft_dtw_gamma $GAMMA \
                --train_epochs $TRAIN_EPOCHS \
                --batch_size $BATCH_SIZE \
                --learning_rate $LEARNING_RATE \
                --des "SoftDTW_gamma_${GAMMA}"
            
            echo "完成 $DATASET $PRED_LEN h Soft-DTW (γ=$GAMMA) 训练"
        done
    done
done

# 实验3: 使用组合损失函数
echo ""
echo "=========================================="
echo "实验3: 组合损失函数 (RMSE + Soft-DTW)"
echo "=========================================="

for DATASET in "lianghe" "yantang" "yinhe"; do
    for PRED_LEN in 24 48 72; do
        for ALPHA in 0.3 0.5 0.7; do
            echo "训练 $DATASET 数据集，预测长度 $PRED_LEN 小时 - 组合损失 (α=$ALPHA)"
            
            python run.py \
                --task_name $TASK \
                --is_training 1 \
                --model_id "${DATASET}_${PRED_LEN}h_combined_a${ALPHA}" \
                --model $MODEL \
                --data $DATASET \
                --features $FEATURES \
                --seq_len $SEQ_LEN \
                --label_len $LABEL_LEN \
                --pred_len $PRED_LEN \
                --loss Combined \
                --combined_alpha $ALPHA \
                --soft_dtw_gamma 1.0 \
                --base_loss RMSE \
                --train_epochs $TRAIN_EPOCHS \
                --batch_size $BATCH_SIZE \
                --learning_rate $LEARNING_RATE \
                --des "Combined_alpha_${ALPHA}"
            
            echo "完成 $DATASET $PRED_LEN h 组合损失 (α=$ALPHA) 训练"
        done
    done
done

# 实验4: 快速测试（仅lianghe数据集）
echo ""
echo "=========================================="
echo "实验4: 快速对比测试 (仅lianghe数据集)"
echo "=========================================="

DATASET="lianghe"
PRED_LEN=24
QUICK_EPOCHS=5

echo "快速测试不同损失函数在 $DATASET 数据集上的表现"

# RMSE基准
python run.py \
    --task_name $TASK \
    --is_training 1 \
    --model_id "${DATASET}_quick_rmse" \
    --model $MODEL \
    --data $DATASET \
    --features $FEATURES \
    --seq_len $SEQ_LEN \
    --label_len $LABEL_LEN \
    --pred_len $PRED_LEN \
    --loss RMSE \
    --train_epochs $QUICK_EPOCHS \
    --batch_size $BATCH_SIZE \
    --learning_rate $LEARNING_RATE \
    --des "Quick_RMSE"

# Soft-DTW
python run.py \
    --task_name $TASK \
    --is_training 1 \
    --model_id "${DATASET}_quick_softdtw" \
    --model $MODEL \
    --data $DATASET \
    --features $FEATURES \
    --seq_len $SEQ_LEN \
    --label_len $LABEL_LEN \
    --pred_len $PRED_LEN \
    --loss SoftDTW \
    --soft_dtw_gamma 1.0 \
    --train_epochs $QUICK_EPOCHS \
    --batch_size $BATCH_SIZE \
    --learning_rate $LEARNING_RATE \
    --des "Quick_SoftDTW"

# 组合损失
python run.py \
    --task_name $TASK \
    --is_training 1 \
    --model_id "${DATASET}_quick_combined" \
    --model $MODEL \
    --data $DATASET \
    --features $FEATURES \
    --seq_len $SEQ_LEN \
    --label_len $LABEL_LEN \
    --pred_len $PRED_LEN \
    --loss Combined \
    --combined_alpha 0.5 \
    --soft_dtw_gamma 1.0 \
    --base_loss RMSE \
    --train_epochs $QUICK_EPOCHS \
    --batch_size $BATCH_SIZE \
    --learning_rate $LEARNING_RATE \
    --des "Quick_Combined"

echo ""
echo "=========================================="
echo "所有实验完成！"
echo "=========================================="
echo "结果文件位置："
echo "- 训练日志: 控制台输出"
echo "- 模型检查点: ./checkpoints/"
echo "- 测试结果: ./test_results/"
echo "- 预测结果: ./results/"
echo "- 汇总结果: result_long_term_forecast.txt"
echo ""
echo "建议下一步："
echo "1. 查看 result_long_term_forecast.txt 比较不同损失函数的性能"
echo "2. 分析 ./results/ 中的预测结果"
echo "3. 根据结果选择最佳的损失函数和参数组合"
echo "=========================================="
