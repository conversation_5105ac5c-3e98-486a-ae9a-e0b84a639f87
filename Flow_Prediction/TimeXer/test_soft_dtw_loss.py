#!/usr/bin/env python3
"""
Test script for Soft-DTW loss function
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from utils.losses import SoftDTWLoss, CombinedLoss, RMSELoss
import os

def test_soft_dtw():
    """Test Soft-DTW loss function"""
    print("Testing Soft-DTW loss function...")
    
    # Create test data
    batch_size, seq_len, features = 2, 10, 1
    
    # Create similar sequences with slight temporal shift
    t = torch.linspace(0, 2*np.pi, seq_len).unsqueeze(0).unsqueeze(-1)
    pred = torch.sin(t).repeat(batch_size, 1, 1)
    target = torch.sin(t + 0.5).repeat(batch_size, 1, 1)  # Shifted version
    
    print(f"Input shapes - pred: {pred.shape}, target: {target.shape}")
    
    # Test different loss functions
    losses = {
        'RMSE': RMSELoss(),
        'Soft-DTW (γ=0.1)': SoftDTWLoss(gamma=0.1),
        'Soft-DTW (γ=1.0)': SoftDTWLoss(gamma=1.0),
        'Soft-DTW (γ=10.0)': SoftDTWLoss(gamma=10.0),
        'Combined (α=0.5)': CombinedLoss(alpha=0.5, gamma=1.0),
        'Combined (α=0.8)': CombinedLoss(alpha=0.8, gamma=1.0),
    }
    
    print("\nLoss comparison for temporally shifted sine waves:")
    print("-" * 50)
    
    for name, loss_fn in losses.items():
        try:
            loss_value = loss_fn(pred, target)
            print(f"{name:20}: {loss_value.item():.6f}")
        except Exception as e:
            print(f"{name:20}: ERROR - {str(e)}")

def test_identical_sequences():
    """Test with identical sequences (should give low loss)"""
    print("\n" + "="*60)
    print("Testing identical sequences...")
    
    batch_size, seq_len, features = 2, 8, 1
    pred = torch.randn(batch_size, seq_len, features)
    target = pred.clone()  # Identical sequences
    
    print(f"Input shapes - pred: {pred.shape}, target: {target.shape}")
    
    soft_dtw_loss = SoftDTWLoss(gamma=1.0)
    rmse_loss = RMSELoss()
    
    soft_dtw_val = soft_dtw_loss(pred, target)
    rmse_val = rmse_loss(pred, target)
    
    print(f"Soft-DTW loss (identical): {soft_dtw_val.item():.6f}")
    print(f"RMSE loss (identical): {rmse_val.item():.6f}")
    
    print("✓ Identical sequences test passed!")

def test_multidimensional():
    """Test with multi-dimensional features"""
    print("\n" + "="*60)
    print("Testing multi-dimensional features...")
    
    batch_size, seq_len, features = 2, 8, 3
    pred = torch.randn(batch_size, seq_len, features)
    target = torch.randn(batch_size, seq_len, features)
    
    print(f"Input shapes - pred: {pred.shape}, target: {target.shape}")
    
    try:
        soft_dtw_loss = SoftDTWLoss(gamma=1.0)
        loss_value = soft_dtw_loss(pred, target)
        print(f"Multi-dimensional Soft-DTW loss: {loss_value.item():.6f}")
        
        combined_loss = CombinedLoss(alpha=0.5, gamma=1.0)
        combined_value = combined_loss(pred, target)
        print(f"Multi-dimensional Combined loss: {combined_value.item():.6f}")
        
        print("✓ Multi-dimensional test passed!")
    except Exception as e:
        print(f"✗ Multi-dimensional test failed: {str(e)}")

def test_gradient_flow():
    """Test gradient flow through Soft-DTW loss"""
    print("\n" + "="*60)
    print("Testing gradient flow...")
    
    batch_size, seq_len, features = 2, 5, 1
    pred = torch.randn(batch_size, seq_len, features, requires_grad=True)
    target = torch.randn(batch_size, seq_len, features)
    
    soft_dtw_loss = SoftDTWLoss(gamma=1.0)
    loss = soft_dtw_loss(pred, target)
    
    print(f"Loss value: {loss.item():.6f}")
    
    # Backward pass
    loss.backward()
    
    if pred.grad is not None:
        print(f"Gradient norm: {pred.grad.norm().item():.6f}")
        print("✓ Gradient flow test passed!")
    else:
        print("✗ Gradient flow test failed: No gradients computed")

def visualize_alignment():
    """Visualize the alignment effect of Soft-DTW"""
    print("\n" + "="*60)
    print("Creating visualization...")
    
    # Create test sequences
    seq_len = 20
    t = torch.linspace(0, 4*np.pi, seq_len)
    
    # Original sequence
    seq1 = torch.sin(t).unsqueeze(0).unsqueeze(-1)
    
    # Shifted and slightly different sequence
    seq2 = torch.sin(t + 1.0).unsqueeze(0).unsqueeze(-1) * 0.8
    
    # Compute losses
    rmse_loss = RMSELoss()
    soft_dtw_loss = SoftDTWLoss(gamma=1.0)
    
    rmse_val = rmse_loss(seq1, seq2)
    soft_dtw_val = soft_dtw_loss(seq1, seq2)
    
    print(f"RMSE loss: {rmse_val.item():.6f}")
    print(f"Soft-DTW loss: {soft_dtw_val.item():.6f}")
    
    # Create plot
    plt.figure(figsize=(12, 6))
    
    plt.subplot(1, 2, 1)
    plt.plot(seq1[0, :, 0].numpy(), 'b-', label='Sequence 1', linewidth=2)
    plt.plot(seq2[0, :, 0].numpy(), 'r--', label='Sequence 2', linewidth=2)
    plt.title('Test Sequences')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    gammas = [0.1, 0.5, 1.0, 2.0, 5.0, 10.0]
    soft_dtw_values = []
    
    for gamma in gammas:
        loss_fn = SoftDTWLoss(gamma=gamma)
        loss_val = loss_fn(seq1, seq2)
        soft_dtw_values.append(loss_val.item())
    
    plt.plot(gammas, soft_dtw_values, 'go-', linewidth=2, markersize=8)
    plt.axhline(y=rmse_val.item(), color='r', linestyle='--', label=f'RMSE: {rmse_val.item():.3f}')
    plt.xlabel('Gamma (γ)')
    plt.ylabel('Loss Value')
    plt.title('Soft-DTW Loss vs Gamma')
    plt.legend()
    plt.grid(True)
    plt.xscale('log')
    
    plt.tight_layout()
    
    # Save plot
    os.makedirs('test_results', exist_ok=True)
    plt.savefig('test_results/soft_dtw_test.png', dpi=300, bbox_inches='tight')
    print("✓ Visualization saved to test_results/soft_dtw_test.png")
    plt.close()

if __name__ == "__main__":
    print("="*60)
    print("Soft-DTW Loss Function Test Suite")
    print("="*60)
    
    try:
        test_soft_dtw()
        test_identical_sequences()
        test_multidimensional()
        test_gradient_flow()
        visualize_alignment()
        
        print("\n" + "="*60)
        print("All tests completed successfully!")
        print("="*60)
        
    except Exception as e:
        print(f"\n✗ Test suite failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
