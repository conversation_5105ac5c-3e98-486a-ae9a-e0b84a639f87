export CUDA_VISIBLE_DEVICES=1

model_name=TimeXer
des='Timexer-MS'


python3 -u run.py \
  --task_name long_term_forecast \
  --is_training 1 \
  --root_path ./dataset/flow/ \
  --data_path yantang111.csv \
  --model_id yantang_168_12 \
  --model $model_name \
  --data custom \
  --features S \
  --loss Combined \
  --combined_alpha 0 \
  --base_loss RMSE \
  --soft_dtw_gamma 1 \
  --inverse \
  --batch_size 8 \
  --seq_len 168 \
  --label_len 96 \
  --pred_len 12 \
  --e_layers 1 \
  --factor 3 \
  --enc_in 1 \
  --dec_in 1 \
  --c_out 1 \
  --des $des \
  --d_model 1024 \
  --itr 1



# python3 -u run.py \
#   --task_name long_term_forecast \
#   --is_training 1 \
#   --root_path ./dataset/flow/ \
#   --data_path yantang.csv \
#   --model_id yantang_168_24 \
#   --model $model_name \
#   --data custom \
#   --features MS \
#   --seq_len 168 \
#   --label_len 48 \
#   --pred_len 24 \
#   --e_layers 1 \
#   --factor 3 \
#   --enc_in 2 \
#   --dec_in 2 \
#   --c_out 2 \
#   --des $des \
#   --d_model 1024 \
#   --itr 1

# python3 -u run.py \
#   --task_name long_term_forecast \
#   --is_training 1 \
#   --root_path ./dataset/flow/ \
#   --data_path yantang.csv \
#   --model_id yantang_168_48 \
#   --model $model_name \
#   --data custom \
#   --features MS \
#   --seq_len 168 \
#   --label_len 48 \
#   --pred_len 48 \
#   --e_layers 1 \
#   --factor 3 \
#   --enc_in 2 \
#   --dec_in 2 \
#   --c_out 2 \
#   --des $des \
#   --d_model 64 \
#   --itr 1

# python3 -u run.py \
#   --task_name long_term_forecast \
#   --is_training 1 \
#   --root_path ./dataset/flow/ \
#   --data_path yantang.csv \
#   --model_id yantang_168_72 \
#   --model $model_name \
#   --data custom \
#   --features MS \
#   --seq_len 168 \
#   --label_len 48 \
#   --pred_len 72 \
#   --e_layers 1 \
#   --factor 3 \
#   --enc_in 2 \
#   --dec_in 2 \
#   --c_out 2 \
#   --des $des \
#   --d_model 64 \
#   --itr 1