export CUDA_VISIBLE_DEVICES=0

model_name=TimeXer
des='Timexer-MS'


python3 -u run.py \
  --task_name long_term_forecast \
  --is_training 1 \
  --root_path ./dataset/flow/ \
  --data_path test.csv \
  --model_id test_168_12 \
  --model $model_name \
  --data custom \
  --features MS \
  --loss Combined \
  --combined_alpha 0 \
  --base_loss RMSE \
  --soft_dtw_gamma 1 \
  --inverse \
  --batch_size 8 \
  --seq_len 168 \
  --label_len 96 \
  --pred_len 12 \
  --e_layers 1 \
  --factor 3 \
  --enc_in 3 \
  --dec_in 3 \
  --c_out 3 \
  --des $des \
  --d_model 1024 \
  --itr 1
