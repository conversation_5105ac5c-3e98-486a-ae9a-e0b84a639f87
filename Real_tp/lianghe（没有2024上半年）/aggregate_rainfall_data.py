#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
汇总两河流域各站点实测雨量数据
功能：
1. 将所有站点数据按时间戳对齐汇总到一张表
2. 根据权重计算面上雨量
"""

import csv
from datetime import datetime
from pathlib import Path
from collections import defaultdict

def load_weights(weight_file):
    """
    加载站点权重数据
    
    Args:
        weight_file (str): 权重文件路径
    
    Returns:
        dict: 站点名称到权重的映射
    """
    weights = {}
    try:
        with open(weight_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                station_name = row['站名'].strip()
                weight = float(row['权重'])
                weights[station_name] = weight
        
        print(f"成功加载 {len(weights)} 个站点的权重数据")
        return weights
        
    except Exception as e:
        print(f"加载权重文件失败: {str(e)}")
        return {}

def normalize_station_name(filename):
    """
    标准化站点名称（从文件名提取）
    
    Args:
        filename (str): 文件名
    
    Returns:
        str: 标准化的站点名称
    """
    # 去掉"_merged.csv"后缀
    name = filename.replace('_merged.csv', '')
    
    # 处理特殊情况的名称映射
    name_mapping = {
        '两河（二）': '两河(二)',
        '拔山八一（八德）': '拔山八一(八德)'
    }
    
    return name_mapping.get(name, name)

def load_station_data(csv_file_path):
    """
    加载单个站点的数据
    
    Args:
        csv_file_path (Path): CSV文件路径
    
    Returns:
        dict: 时间戳到雨量值的映射
    """
    data = {}
    try:
        with open(csv_file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                timestamp = row['时间']
                rainfall = float(row['时段雨量'])
                data[timestamp] = rainfall
        
        return data
        
    except Exception as e:
        print(f"加载站点数据失败 {csv_file_path.name}: {str(e)}")
        return {}

def aggregate_rainfall_data():
    """
    汇总所有站点的雨量数据并计算面上雨量
    """
    print("两河流域雨量数据汇总工具")
    print("=" * 60)
    
    # 加载权重数据
    weights = load_weights('weight.csv')
    if not weights:
        print("无法加载权重数据，程序退出")
        return

    # 获取merge文件夹中的所有CSV文件
    merge_dir = Path('merge')
    csv_files = list(merge_dir.glob('*_merged.csv'))
    
    if not csv_files:
        print("未找到站点数据文件")
        return
    
    print(f"发现 {len(csv_files)} 个站点数据文件")
    
    # 加载所有站点数据
    all_station_data = {}
    all_timestamps = set()
    
    for csv_file in sorted(csv_files):
        station_name = normalize_station_name(csv_file.name)
        print(f"正在加载: {station_name} ({csv_file.name})")
        
        station_data = load_station_data(csv_file)
        if station_data:
            all_station_data[station_name] = station_data
            all_timestamps.update(station_data.keys())
            print(f"  加载 {len(station_data)} 条记录")
        else:
            print(f"  加载失败")
    
    # 检查权重文件中的站点是否都有数据
    missing_stations = []
    for station_name in weights.keys():
        if station_name not in all_station_data:
            missing_stations.append(station_name)
    
    if missing_stations:
        print(f"\n警告：以下站点在权重文件中但缺少数据文件:")
        for station in missing_stations:
            print(f"  - {station}")
    
    # 获取所有时间戳并排序
    sorted_timestamps = sorted(all_timestamps)
    print(f"\n时间范围: {sorted_timestamps[0]} 到 {sorted_timestamps[-1]}")
    print(f"总时间点数: {len(sorted_timestamps)}")
    
    # 创建汇总表
    print("\n开始创建汇总表...")
    
    # 确定要包含的站点（有数据且有权重的站点）
    valid_stations = []
    for station_name in weights.keys():
        if station_name in all_station_data:
            valid_stations.append(station_name)
    
    print(f"有效站点数: {len(valid_stations)}")
    for station in valid_stations:
        print(f"  - {station} (权重: {weights[station]})")
    
    # 生成汇总数据
    output_file = Path('两河流域雨量汇总.csv')
    
    try:
        with open(output_file, 'w', encoding='utf-8-sig', newline='') as f:
            # 创建标题行
            fieldnames = ['时间'] + valid_stations + ['面上雨量']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            # 写入数据
            for timestamp in sorted_timestamps:
                row = {'时间': timestamp}
                
                # 获取各站点数据
                station_values = []
                for station in valid_stations:
                    value = all_station_data[station].get(timestamp, 0.0)
                    row[station] = value
                    station_values.append(value)
                
                # 计算面上雨量（加权平均）
                weighted_sum = 0.0
                total_weight = 0.0
                
                for i, station in enumerate(valid_stations):
                    weight = weights[station]
                    value = station_values[i]
                    weighted_sum += weight * value
                    total_weight += weight
                
                # 面上雨量
                area_rainfall = weighted_sum / total_weight if total_weight > 0 else 0.0
                row['面上雨量'] = round(area_rainfall, 4)
                
                writer.writerow(row)
        
        print(f"\n✅ 汇总完成！")
        print(f"输出文件: {output_file}")
        print(f"数据行数: {len(sorted_timestamps)}")
        print(f"包含站点: {len(valid_stations)} 个")
        
        # 显示前几行数据预览
        print(f"\n前5行数据预览:")
        with open(output_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            for i, row in enumerate(reader):
                if i >= 6:  # 标题行 + 5行数据
                    break
                print(f"  {row}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建汇总文件失败: {str(e)}")
        return False

def main():
    """
    主函数
    """
    aggregate_rainfall_data()

if __name__ == "__main__":
    main()
