#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理CSV文件：时间格式转换、排序和合并
"""

import os
import csv
import pandas as pd
from datetime import datetime
from pathlib import Path

def process_csv_file(csv_file_path):
    """
    处理单个CSV文件：删除前三行以及第一列
    
    Args:
        csv_file_path (str): CSV文件路径
    
    Returns:
        bool: 是否处理成功
    """
    try:
        # 读取原始文件
        with open(csv_file_path, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            all_rows = list(reader)
        
        if len(all_rows) <= 3:
            print(f"   警告：文件行数不足，跳过处理")
            return False
        
        # 删除前三行，保留从第4行开始的数据
        data_rows = all_rows[3:]
        
        # 删除第一列（序号列），保留第二列和第三列
        processed_rows = []
        for row in data_rows:
            if len(row) >= 2:
                # 保留第二列（时间）和第三列（时段雨量）
                processed_row = row[1:]  # 删除第一列
                processed_rows.append(processed_row)
        
        # 添加新的标题行
        header = ['时间', '时段雨量']
        final_rows = [header] + processed_rows
        
        # 写回文件
        with open(csv_file_path, 'w', encoding='utf-8', newline='') as f:
            writer = csv.writer(f)
            writer.writerows(final_rows)
        
        return True
        
    except Exception as e:
        print(f"   错误：{str(e)}")
        return False

def process_all_csv_files():
    """
    处理当前目录下的所有CSV文件
    """
    current_dir = Path(".")
    
    # 查找所有CSV文件
    csv_files = list(current_dir.glob("*.csv"))
    
    if not csv_files:
        print("未找到CSV文件")
        return
    
    print(f"发现 {len(csv_files)} 个CSV文件，开始处理...")
    print("=" * 60)
    
    successful_count = 0
    failed_count = 0
    
    for csv_file in sorted(csv_files):
        try:
            print(f"\n正在处理: {csv_file.name}")
            
            # 获取原始文件信息
            with open(csv_file, 'r', encoding='utf-8') as f:
                original_lines = f.readlines()
            original_line_count = len(original_lines)
            
            # 处理文件
            if process_csv_file(csv_file):
                # 获取处理后的文件信息
                with open(csv_file, 'r', encoding='utf-8') as f:
                    processed_lines = f.readlines()
                processed_line_count = len(processed_lines)
                
                print(f"✅ 处理成功")
                print(f"   原始行数: {original_line_count}")
                print(f"   处理后行数: {processed_line_count}")
                print(f"   删除行数: {original_line_count - processed_line_count}")
                
                # 显示处理后的前几行
                print(f"   处理后前3行预览:")
                for i, line in enumerate(processed_lines[:3]):
                    print(f"     行{i+1}: {line.strip()}")
                
                successful_count += 1
            else:
                print(f"❌ 处理失败")
                failed_count += 1
                
        except Exception as e:
            print(f"❌ 处理失败: {csv_file.name}")
            print(f"   错误信息: {str(e)}")
            failed_count += 1
        
        print("-" * 40)
    
    # 最终统计
    print(f"\n🎉 处理完成！")
    print(f"成功处理: {successful_count} 个文件")
    print(f"处理失败: {failed_count} 个文件")
    print(f"总计文件: {len(csv_files)} 个")

if __name__ == "__main__":
    print("CSV文件处理工具")
    print("功能：删除前三行以及第一列")
    print("=" * 60)
    process_all_csv_files()
