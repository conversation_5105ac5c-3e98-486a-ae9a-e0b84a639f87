#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查和补全CSV文件中缺失的时间点
功能：
1. 检查每个CSV文件的时间序列完整性
2. 补全缺失的时间点，时段雨量值用0填充
3. 确保时间序列连续且按小时递增
"""

import csv
from datetime import datetime, timedelta
from pathlib import Path

def parse_datetime(time_str):
    """
    解析时间字符串为datetime对象

    Args:
        time_str (str): 时间字符串

    Returns:
        datetime: 解析后的时间对象
    """
    return datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')

def get_time_range(csv_file_path):
    """
    获取CSV文件的时间范围

    Args:
        csv_file_path (Path): CSV文件路径

    Returns:
        tuple: (start_time, end_time) 或 (None, None) 如果出错
    """
    try:
        with open(csv_file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            times = []

            for row in reader:
                if '时间' in row:
                    time_obj = parse_datetime(row['时间'])
                    times.append(time_obj)

        if not times:
            return None, None

        start_time = min(times)
        end_time = max(times)

        return start_time, end_time

    except Exception as e:
        print(f"   错误：无法读取文件 - {str(e)}")
        return None, None

def generate_complete_time_series(start_time, end_time):
    """
    生成完整的小时时间序列
    
    Args:
        start_time (datetime): 开始时间
        end_time (datetime): 结束时间
    
    Returns:
        list: 完整的时间序列列表
    """
    time_series = []
    current_time = start_time
    
    while current_time <= end_time:
        time_series.append(current_time)
        current_time += timedelta(hours=1)
    
    return time_series

def fill_missing_timestamps(csv_file_path):
    """
    补全CSV文件中缺失的时间点

    Args:
        csv_file_path (Path): CSV文件路径

    Returns:
        bool: 是否处理成功
    """
    try:
        # 读取原始数据
        original_data = {}
        with open(csv_file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                if '时间' in row and '时段雨量' in row:
                    time_obj = parse_datetime(row['时间'])
                    original_data[time_obj] = float(row['时段雨量'])

        if not original_data:
            print(f"   文件格式错误或为空")
            return False

        # 获取时间范围
        start_time = min(original_data.keys())
        end_time = max(original_data.keys())

        # 生成完整的时间序列
        complete_time_series = generate_complete_time_series(start_time, end_time)

        # 创建完整的数据集，缺失的时间点用0填充
        complete_data = []
        for time_obj in complete_time_series:
            rainfall = original_data.get(time_obj, 0)  # 缺失的用0填充
            complete_data.append({
                '时间': time_obj.strftime('%Y-%m-%d %H:%M:%S'),
                '时段雨量': rainfall
            })

        # 统计信息
        original_count = len(original_data)
        complete_count = len(complete_data)
        added_count = complete_count - original_count

        # 写回文件
        with open(csv_file_path, 'w', encoding='utf-8', newline='') as f:
            fieldnames = ['时间', '时段雨量']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(complete_data)

        print(f"   原始记录数: {original_count}")
        print(f"   补全后记录数: {complete_count}")
        print(f"   新增记录数: {added_count}")
        print(f"   时间范围: {start_time.strftime('%Y-%m-%d %H:%M')} 到 {end_time.strftime('%Y-%m-%d %H:%M')}")

        return True

    except Exception as e:
        print(f"   错误：{str(e)}")
        return False

def process_all_csv_files():
    """
    处理merge文件夹下的所有CSV文件
    """
    merge_dir = Path("merge")
    
    if not merge_dir.exists():
        print("错误：merge文件夹不存在")
        return
    
    # 查找所有CSV文件
    csv_files = list(merge_dir.glob("*.csv"))
    
    if not csv_files:
        print("merge文件夹中未找到CSV文件")
        return
    
    print(f"发现 {len(csv_files)} 个CSV文件，开始检查和补全时间点...")
    print("=" * 70)
    
    successful_count = 0
    failed_count = 0
    total_added = 0
    
    for csv_file in sorted(csv_files):
        try:
            print(f"\n正在处理: {csv_file.name}")
            
            # 获取原始记录数
            original_count = 0
            try:
                with open(csv_file, 'r', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    original_count = sum(1 for row in reader) - 1  # 减去标题行
            except:
                pass

            # 补全时间点
            if fill_missing_timestamps(csv_file):
                # 读取处理后的记录数
                processed_count = 0
                try:
                    with open(csv_file, 'r', encoding='utf-8') as f:
                        reader = csv.reader(f)
                        processed_count = sum(1 for row in reader) - 1  # 减去标题行
                except:
                    pass

                added_count = processed_count - original_count

                print(f"✅ 处理成功")

                # 显示处理后的前几行
                print(f"   处理后前5行预览:")
                try:
                    with open(csv_file, 'r', encoding='utf-8') as f:
                        reader = csv.DictReader(f)
                        for i, row in enumerate(reader):
                            if i >= 5:
                                break
                            print(f"     行{i+1}: {row['时间']}, {row['时段雨量']}")
                except:
                    print(f"     无法读取预览")

                successful_count += 1
                total_added += added_count
            else:
                print(f"❌ 处理失败")
                failed_count += 1
                
        except Exception as e:
            print(f"❌ 处理失败: {csv_file.name}")
            print(f"   错误信息: {str(e)}")
            failed_count += 1
        
        print("-" * 50)
    
    # 最终统计
    print(f"\n🎉 处理完成！")
    print(f"成功处理: {successful_count} 个文件")
    print(f"处理失败: {failed_count} 个文件")
    print(f"总计文件: {len(csv_files)} 个")
    print(f"总计新增记录: {total_added} 条")

def main():
    """
    主函数
    """
    print("CSV文件时间点补全工具")
    print("功能：检查和补全缺失的时间点，时段雨量值用0填充")
    print("=" * 70)
    
    process_all_csv_files()

if __name__ == "__main__":
    main()
