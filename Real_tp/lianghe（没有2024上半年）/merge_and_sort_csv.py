#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理CSV文件：时间格式转换、排序和合并
功能：
1. 将时间格式从"月-日 小时"转换为标准时间格式
2. 按时间顺序排序（原数据是倒序）
3. 合并每个水文站的2024下半年和2025年数据
"""

import csv
from datetime import datetime
from pathlib import Path
import re

def parse_time_string(time_str, year):
    """
    解析时间字符串，转换为标准时间格式
    
    Args:
        time_str (str): 时间字符串，格式如"12-31 23"
        year (int): 年份
    
    Returns:
        datetime: 标准时间对象
    """
    try:
        # 解析"月-日 小时"格式
        parts = time_str.strip().split()
        if len(parts) != 2:
            return None
        
        date_part = parts[0]  # "12-31"
        hour_part = parts[1]  # "23"
        
        # 分离月和日
        month_day = date_part.split('-')
        if len(month_day) != 2:
            return None
        
        month = int(month_day[0])
        day = int(month_day[1])
        hour = int(hour_part)
        
        # 创建datetime对象
        dt = datetime(year, month, day, hour, 0, 0)
        return dt
        
    except (ValueError, IndexError) as e:
        print(f"   警告：无法解析时间字符串 '{time_str}': {e}")
        return None

def process_single_csv(csv_file_path, year):
    """
    处理单个CSV文件
    
    Args:
        csv_file_path (Path): CSV文件路径
        year (int): 年份
    
    Returns:
        list: 处理后的数据列表，每个元素为[datetime, rainfall]
    """
    data_list = []
    
    try:
        with open(csv_file_path, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            header = next(reader)  # 跳过标题行
            
            for row in reader:
                if len(row) >= 2:
                    time_str = row[0]
                    rainfall = row[1]
                    
                    # 解析时间
                    dt = parse_time_string(time_str, year)
                    if dt:
                        data_list.append([dt, rainfall])
        
        print(f"   读取 {len(data_list)} 条记录")
        return data_list
        
    except Exception as e:
        print(f"   错误：{str(e)}")
        return []

def merge_station_data(station_name):
    """
    合并单个水文站的2024下半年和2025年数据
    
    Args:
        station_name (str): 水文站名称
    """
    print(f"\n处理水文站: {station_name}")
    print("-" * 50)
    
    # 文件路径
    file_2024 = Path(f"{station_name}2024下半年.csv")
    file_2025 = Path(f"{station_name}2025.csv")
    
    all_data = []
    
    # 处理2024下半年数据
    if file_2024.exists():
        print(f"正在处理: {file_2024.name}")
        data_2024 = process_single_csv(file_2024, 2024)
        all_data.extend(data_2024)
    else:
        print(f"警告：未找到文件 {file_2024.name}")
    
    # 处理2025年数据
    if file_2025.exists():
        print(f"正在处理: {file_2025.name}")
        data_2025 = process_single_csv(file_2025, 2025)
        all_data.extend(data_2025)
    else:
        print(f"警告：未找到文件 {file_2025.name}")
    
    if not all_data:
        print("没有数据可处理")
        return False
    
    # 按时间排序（升序）
    all_data.sort(key=lambda x: x[0])
    
    # 生成输出文件名
    output_file = Path(f"{station_name}_merged.csv")
    
    # 写入合并后的数据
    try:
        with open(output_file, 'w', encoding='utf-8', newline='') as f:
            writer = csv.writer(f)
            
            # 写入标题行
            writer.writerow(['时间', '时段雨量'])
            
            # 写入数据
            for dt, rainfall in all_data:
                time_str = dt.strftime('%Y-%m-%d %H:%M:%S')
                writer.writerow([time_str, rainfall])
        
        print(f"✅ 合并完成: {output_file.name}")
        print(f"   总记录数: {len(all_data)}")
        print(f"   时间范围: {all_data[0][0].strftime('%Y-%m-%d %H:%M')} 到 {all_data[-1][0].strftime('%Y-%m-%d %H:%M')}")
        
        # 显示前几行数据预览
        print(f"   前5行数据预览:")
        for i, (dt, rainfall) in enumerate(all_data[:5]):
            time_str = dt.strftime('%Y-%m-%d %H:%M:%S')
            print(f"     {i+1}: {time_str}, {rainfall}")
        
        return True
        
    except Exception as e:
        print(f"❌ 写入文件失败: {str(e)}")
        return False

def get_station_names():
    """
    获取所有水文站名称
    
    Returns:
        list: 水文站名称列表
    """
    current_dir = Path(".")
    csv_files = list(current_dir.glob("*2024下半年.csv"))
    
    station_names = []
    for file in csv_files:
        # 提取水文站名称（去掉"2024下半年.csv"后缀）
        station_name = file.name.replace("2024下半年.csv", "")
        station_names.append(station_name)
    
    return sorted(station_names)

def main():
    """
    主函数
    """
    print("CSV文件时间格式转换和合并工具")
    print("=" * 60)
    
    # 获取所有水文站名称
    station_names = get_station_names()
    
    if not station_names:
        print("未找到水文站数据文件")
        return
    
    print(f"发现 {len(station_names)} 个水文站:")
    for i, name in enumerate(station_names, 1):
        print(f"  {i}. {name}")
    
    print("\n开始处理...")
    print("=" * 60)
    
    successful_count = 0
    failed_count = 0
    
    for station_name in station_names:
        try:
            if merge_station_data(station_name):
                successful_count += 1
            else:
                failed_count += 1
        except Exception as e:
            print(f"❌ 处理失败: {station_name}")
            print(f"   错误信息: {str(e)}")
            failed_count += 1
    
    # 最终统计
    print("\n" + "=" * 60)
    print(f"🎉 处理完成！")
    print(f"成功处理: {successful_count} 个水文站")
    print(f"处理失败: {failed_count} 个水文站")
    print(f"总计水文站: {len(station_names)} 个")

if __name__ == "__main__":
    main()
