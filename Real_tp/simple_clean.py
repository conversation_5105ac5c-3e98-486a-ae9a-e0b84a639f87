#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import glob

def clean_file(filepath):
    """清理单个文件中的01-01数据"""
    print(f"检查文件: {os.path.basename(filepath)}")
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        original_count = len(lines)
        cleaned_lines = []
        removed_count = 0
        
        for i, line in enumerate(lines):
            # 检查是否包含01-01
            if '01-01' in line and i > 0:  # 跳过表头
                print(f"  删除行: {line.strip()}")
                removed_count += 1
            else:
                cleaned_lines.append(line)
        
        if removed_count > 0:
            # 写回文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.writelines(cleaned_lines)
            print(f"  ✅ 删除了 {removed_count} 行01-01数据")
            return True
        else:
            print(f"  ✓ 未发现01-01数据")
            return False
            
    except Exception as e:
        print(f"  ❌ 错误: {e}")
        return False

def main():
    print("开始清理2024下半年CSV文件中的01-01数据")
    print("=" * 50)
    
    # 查找所有2024下半年的CSV文件
    patterns = [
        'Real_tp/lianghe1/origin_data/*2024下半年*.csv',
        'Real_tp/yantang/origin_data/*2024下半年*.csv', 
        'Real_tp/yinhe/origin_data/*2024下半年*.csv'
    ]
    
    total_files = 0
    cleaned_files = 0
    
    for pattern in patterns:
        files = glob.glob(pattern)
        print(f"\n处理模式: {pattern}")
        print(f"找到 {len(files)} 个文件")
        
        for filepath in sorted(files):
            total_files += 1
            if clean_file(filepath):
                cleaned_files += 1
    
    print(f"\n总结:")
    print(f"检查了 {total_files} 个文件")
    print(f"清理了 {cleaned_files} 个文件")

if __name__ == "__main__":
    main()
