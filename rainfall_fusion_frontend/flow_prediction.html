<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基于融合产品的降雨流量预测系统</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            padding: 0;
            overflow: hidden;
        }
        
        .container {
            width: 100%;
            height: 100vh;
            margin: 0;
            background: transparent;
            border-radius: 0;
            box-shadow: none;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 15px 30px;
            flex-shrink: 0;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            height: 80px;
            display: flex;
            align-items: center;
        }

        .nav-container {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-left h1 {
            font-size: 1.8em;
            margin-bottom: 5px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .nav-left p {
            font-size: 0.9em;
            opacity: 0.9;
            margin: 0;
        }

        .main-nav {
            display: flex;
            gap: 20px;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.9em;
            border: 2px solid transparent;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .main-content {
            display: flex;
            height: 100vh;
            overflow: hidden;
        }
        
        .left-panel {
            width: 40%;
            flex-shrink: 0;
            position: fixed;
            left: 0;
            top: 80px;
            bottom: 0;
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            overflow-y: auto;
            z-index: 100;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
        }

        .left-panel::-webkit-scrollbar {
            width: 6px;
        }

        .left-panel::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .left-panel::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .left-panel::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        
        .right-panel {
            margin-left: 40%;
            width: 60%;
            height: calc(100vh - 80px);
            margin-top: 80px;
            overflow-y: auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
        }

        .right-panel::-webkit-scrollbar {
            width: 8px;
        }

        .right-panel::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .right-panel::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .right-panel::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .input-section {
            background: transparent;
            padding: 0;
            border-radius: 0;
            box-shadow: none;
            height: 100%;
        }

        .input-group {
            display: flex;
            flex-direction: column;
            margin-bottom: 20px;
        }

        .input-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c3e50;
            font-size: 0.9em;
        }

        .input-group input, .input-group select {
            padding: 10px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 13px;
            transition: all 0.3s ease;
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .fusion-result-section {
            background: white;
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #e1e8ed;
            margin-bottom: 20px;
        }

        .fusion-result-section h4 {
            margin-bottom: 10px;
            color: #2c3e50;
            font-size: 0.9em;
        }

        .fusion-display {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4caf50;
            font-family: monospace;
            font-size: 12px;
            min-height: 100px;
            color: #666;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .file-upload-section {
            margin-top: 20px;
        }

        .file-upload-area {
            border: 2px dashed #667eea;
            border-radius: 10px;
            padding: 12px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
            position: relative;
        }

        .file-upload-area:hover {
            border-color: #5a6fd8;
            background: #f8f9ff;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }

        .file-upload-area.dragover {
            border-color: #4caf50;
            background: #f1f8e9;
        }

        .file-upload-icon {
            font-size: 1.5em;
            margin-bottom: 6px;
            color: #667eea;
        }

        .file-upload-text {
            color: #2c3e50;
            font-size: 13px;
        }

        .file-upload-text strong {
            color: #667eea;
        }

        .file-upload-status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 8px;
            font-size: 13px;
        }

        .file-upload-status.success {
            background: #e8f5e8;
            color: #2e7d32;
            border-left: 4px solid #4caf50;
        }

        .file-upload-status.error {
            background: #ffebee;
            color: #c62828;
            border-left: 4px solid #f44336;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            width: 100%;
            margin-top: 20px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .result-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 25px;
            overflow: hidden;
            border: 1px solid #e1e8ed;
        }

        .result-card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            font-size: 1.3em;
            font-weight: 600;
        }

        .result-card-content {
            padding: 25px;
        }

        .chart-container {
            margin-top: 20px;
            height: 500px;
            position: relative;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .export-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }

        .export-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.5);
            background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
        }

        .export-btn:active {
            transform: translateY(0);
        }

        @media (max-width: 768px) {
            body {
                overflow: auto;
            }
            
            .header {
                position: relative;
                height: auto;
                padding: 15px;
            }
            
            .main-content {
                flex-direction: column;
                height: auto;
                overflow: visible;
            }
            
            .left-panel {
                width: 100%;
                position: static;
                height: auto;
                background: rgba(248, 249, 250, 0.95);
                margin: 0;
                padding: 20px;
                box-shadow: none;
            }
            
            .right-panel {
                width: 100%;
                margin-left: 0;
                margin-top: 0;
                height: auto;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="nav-container">
                <div class="nav-left">
                    <h1>📊 基于融合产品的降雨流量预测系统</h1>
                    <p>Rainfall-Flow Prediction System Based on Fusion Products</p>
                </div>
                <div class="nav-right">
                    <nav class="main-nav">
                        <a href="11.html" class="nav-link">🌧️ 降雨产品融合</a>
                        <a href="flow_prediction.html" class="nav-link active">📊 流量预测系统</a>
                    </nav>
                </div>
            </div>
        </div>

        <div class="main-content">
            <!-- 左侧面板 - 预测配置 -->
            <div class="left-panel">
                <div class="input-section">
                    <h3 style="margin-bottom: 20px; color: #2c3e50; text-align: center;">⚙️ 预测系统配置</h3>
                    
                    <!-- 融合结果显示 -->
                    <div class="fusion-result-section">
                        <h4>🌧️ 多源降雨产品融合结果</h4>
                        <div class="fusion-display" id="fusionDisplay">
                            请先在降雨产品融合系统中完成数据融合
                        </div>
                    </div>
                    
                    <!-- 流域选择 -->
                    <div class="input-group">
                        <label for="watershed">🏞️ 流域选择:</label>
                        <select id="watershed">
                            <option value="lianghe">两河流域</option>
                            <option value="yantang">沿塘流域</option>
                            <option value="yinhe">银河流域</option>
                        </select>
                    </div>
                    
                    <!-- 历史窗口长度 -->
                    <div class="input-group">
                        <label for="historyWindow">📅 历史窗口长度 (小时):</label>
                        <input type="number" id="historyWindow" value="24" min="1" max="168">
                    </div>
                    
                    <!-- 预测窗口长度 -->
                    <div class="input-group">
                        <label for="predictionWindow">🔮 预测窗口长度 (小时):</label>
                        <input type="number" id="predictionWindow" value="12" min="1" max="72">
                    </div>
                    
                    <!-- 历史流量数据上传 -->
                    <div class="file-upload-section">
                        <h4 style="margin-bottom: 15px; color: #2c3e50; font-size: 0.9em;">📁 历史窗口实测流量数据上传</h4>
                        <div class="file-upload-container">
                            <input type="file" id="flowFileInput" accept=".xlsx,.xls,.csv" style="display: none;" onchange="handleFlowFileUpload(event)">
                            <div class="file-upload-area" onclick="document.getElementById('flowFileInput').click()">
                                <div class="file-upload-icon">📊</div>
                                <div class="file-upload-text">
                                    <strong>点击上传流量数据文件</strong>
                                    <br>
                                    <small>支持 .xlsx, .xls, .csv 格式</small>
                                </div>
                            </div>
                            <div id="flowFileUploadStatus" class="file-upload-status" style="display: none;"></div>
                        </div>
                        <div style="margin-top: 10px; padding: 8px 12px; background: #f0f8ff; border-radius: 6px; border-left: 3px solid #667eea;">
                            <small style="color: #666;">
                                <strong>📋 文件格式要求:</strong> 包含时间戳和流量值两列，时间戳格式为 YYYY-MM-DD HH:MM:SS
                            </small>
                        </div>
                    </div>
                    
                    <!-- 预测按钮 -->
                    <button class="btn btn-primary" onclick="performFlowPrediction()">🚀 开始流量预测</button>
                    
                    <div style="margin-top: 15px; padding: 10px; background: #f0f8ff; border-radius: 8px; font-size: 11px; color: #666;">
                        <strong>💡 使用说明:</strong> 
                        系统将根据选择的流域和窗口长度自动选择相应的权重进行流量预测推理
                    </div>
                </div>
            </div>

            <!-- 右侧面板 - 预测结果 -->
            <div class="right-panel">
                <!-- 欢迎信息 -->
                <div id="welcomeMessage" class="result-card">
                    <div class="result-card-header">📊 流量预测系统</div>
                    <div class="result-card-content">
                        <div style="text-align: center; padding: 40px 20px;">
                            <div style="font-size: 4em; margin-bottom: 20px;">🌊</div>
                            <h3 style="color: #2c3e50; margin-bottom: 15px;">开始您的流量预测</h3>
                            <p style="color: #666; margin-bottom: 25px; line-height: 1.6;">
                                请在左侧面板中配置预测参数并上传历史流量数据，然后开始流量预测。
                            </p>
                            <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                                <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; flex: 1; min-width: 200px;">
                                    <h4 style="color: #2e7d32; margin-bottom: 8px;">🏞️ 流域选择</h4>
                                    <p style="font-size: 14px; color: #666;">选择两河、沿塘或银河流域</p>
                                </div>
                                <div style="background: #e3f2fd; padding: 15px; border-radius: 10px; flex: 1; min-width: 200px;">
                                    <h4 style="color: #1976d2; margin-bottom: 8px;">📊 智能预测</h4>
                                    <p style="font-size: 14px; color: #666;">基于融合产品的流量预测</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 预测结果 -->
                <div id="predictionResults" class="result-card" style="display: none;">
                    <div class="result-card-header">
                        🌊 流量预测结果
                        <div style="float: right; display: flex; gap: 10px;">
                            <button class="export-btn" onclick="exportPredictionData('csv')" title="导出CSV格式">
                                📄 CSV
                            </button>
                            <button class="export-btn" onclick="exportPredictionData('excel')" title="导出Excel格式">
                                📊 Excel
                            </button>
                            <button class="export-btn" onclick="exportChartImage()" title="导出图表图片">
                                🖼️ PNG
                            </button>
                        </div>
                    </div>
                    <div class="result-card-content">
                        <div class="chart-container">
                            <canvas id="predictionChart"></canvas>
                        </div>
                        <div id="predictionSummary" style="margin-top: 20px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentChart = null;
        let fusionData = null;
        let historicalFlowData = null;
        let currentPredictionData = null;
        let currentHistoricalData = null;
        let currentWatershed = null;

        // 模拟从融合系统获取数据
        function loadFusionData() {
            // 这里应该从实际的融合系统获取数据
            // 现在使用模拟数据
            const mockFusionData = [
                { time: '2024-01-01 09:00', value: 2.5 },
                { time: '2024-01-01 10:00', value: 3.2 },
                { time: '2024-01-01 11:00', value: 1.8 },
                { time: '2024-01-01 12:00', value: 4.1 },
                { time: '2024-01-01 13:00', value: 2.9 }
            ];

            fusionData = mockFusionData;
            updateFusionDisplay();
        }

        function updateFusionDisplay() {
            const fusionDisplay = document.getElementById('fusionDisplay');
            if (fusionData && fusionData.length > 0) {
                fusionDisplay.innerHTML = `
                    <div style="text-align: left;">
                        <strong>融合降雨数据 (最近5小时):</strong><br>
                        ${fusionData.map(item =>
                            `${item.time}: ${item.value.toFixed(2)} mm`
                        ).join('<br>')}
                    </div>
                `;
                fusionDisplay.style.color = '#2c3e50';
            } else {
                fusionDisplay.innerHTML = '请先在降雨产品融合系统中完成数据融合';
                fusionDisplay.style.color = '#666';
            }
        }

        // 处理流量文件上传
        function handleFlowFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            const statusDiv = document.getElementById('flowFileUploadStatus');
            statusDiv.style.display = 'block';
            statusDiv.className = 'file-upload-status';
            statusDiv.innerHTML = '📤 正在读取流量数据文件...';

            const reader = new FileReader();

            if (file.name.endsWith('.csv')) {
                reader.onload = function(e) {
                    try {
                        parseCSVFlowData(e.target.result, file.name);
                    } catch (error) {
                        showFlowUploadError('CSV文件读取失败: ' + error.message);
                    }
                };
                reader.readAsText(file);
            } else {
                reader.onload = function(e) {
                    try {
                        const data = new Uint8Array(e.target.result);
                        const workbook = XLSX.read(data, { type: 'array' });
                        const firstSheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[firstSheetName];
                        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                        parseExcelFlowData(jsonData, file.name);
                    } catch (error) {
                        showFlowUploadError('Excel文件读取失败: ' + error.message);
                    }
                };
                reader.readAsArrayBuffer(file);
            }
        }

        function parseCSVFlowData(csvText, fileName) {
            const lines = csvText.trim().split('\n');
            if (lines.length < 2) {
                throw new Error('CSV文件数据不足');
            }

            const flowData = [];
            let startRow = 0;

            // 检查是否有标题行
            const firstLine = lines[0].split(',');
            if (firstLine.some(cell =>
                cell.toLowerCase().includes('time') ||
                cell.toLowerCase().includes('flow') ||
                cell.includes('时间') ||
                cell.includes('流量')
            )) {
                startRow = 1;
            }

            for (let i = startRow; i < lines.length; i++) {
                const columns = lines[i].split(',');
                if (columns.length >= 2) {
                    const timeStr = columns[0].trim();
                    const flowValue = parseFloat(columns[1].trim());

                    if (!isNaN(flowValue)) {
                        flowData.push({
                            time: timeStr,
                            flow: flowValue
                        });
                    }
                }
            }

            if (flowData.length === 0) {
                throw new Error('未找到有效的流量数据');
            }

            historicalFlowData = flowData;
            showFlowUploadSuccess(fileName, flowData.length);
        }

        function parseExcelFlowData(jsonData, fileName) {
            if (jsonData.length < 2) {
                throw new Error('Excel文件数据不足');
            }

            const flowData = [];
            let startRow = 0;

            // 检查第一行是否为标题行
            const firstRow = jsonData[0];
            if (firstRow.some(cell =>
                typeof cell === 'string' && (
                    cell.toLowerCase().includes('time') ||
                    cell.toLowerCase().includes('flow') ||
                    cell.includes('时间') ||
                    cell.includes('流量')
                )
            )) {
                startRow = 1;
            }

            for (let i = startRow; i < jsonData.length; i++) {
                const row = jsonData[i];
                if (row.length >= 2) {
                    const timeStr = row[0];
                    const flowValue = parseFloat(row[1]);

                    if (!isNaN(flowValue)) {
                        flowData.push({
                            time: timeStr,
                            flow: flowValue
                        });
                    }
                }
            }

            if (flowData.length === 0) {
                throw new Error('未找到有效的流量数据');
            }

            historicalFlowData = flowData;
            showFlowUploadSuccess(fileName, flowData.length);
        }

        function showFlowUploadSuccess(fileName, dataCount) {
            const statusDiv = document.getElementById('flowFileUploadStatus');
            statusDiv.className = 'file-upload-status success';
            statusDiv.innerHTML = `
                ✅ 流量数据上传成功！<br>
                📁 文件名: ${fileName}<br>
                📊 数据点数: ${dataCount} 个<br>
                📅 时间范围: ${historicalFlowData[0].time} 至 ${historicalFlowData[historicalFlowData.length-1].time}
            `;
        }

        function showFlowUploadError(message) {
            const statusDiv = document.getElementById('flowFileUploadStatus');
            statusDiv.className = 'file-upload-status error';
            statusDiv.innerHTML = `❌ ${message}`;
        }

        // 执行流量预测
        function performFlowPrediction() {
            if (!fusionData || fusionData.length === 0) {
                alert('请先在降雨产品融合系统中完成数据融合');
                return;
            }

            if (!historicalFlowData || historicalFlowData.length === 0) {
                alert('请先上传历史流量数据');
                return;
            }

            const watershed = document.getElementById('watershed').value;
            const historyWindow = parseInt(document.getElementById('historyWindow').value);
            const predictionWindow = parseInt(document.getElementById('predictionWindow').value);

            // 显示加载状态
            document.getElementById('welcomeMessage').style.display = 'none';
            document.getElementById('predictionResults').style.display = 'block';

            // 模拟预测计算
            setTimeout(() => {
                generatePredictionResults(watershed, historyWindow, predictionWindow);
            }, 1000);
        }

        function generatePredictionResults(watershed, historyWindow, predictionWindow) {
            // 模拟预测结果
            const historicalData = historicalFlowData.slice(-historyWindow);
            const predictedData = [];

            // 简单的预测模拟（实际应用中会使用训练好的模型）
            const lastFlow = historicalData[historicalData.length - 1].flow;
            const avgRainfall = fusionData.reduce((sum, item) => sum + item.value, 0) / fusionData.length;

            for (let i = 1; i <= predictionWindow; i++) {
                const baseFlow = lastFlow + (avgRainfall * 0.5 * Math.random());
                const noise = (Math.random() - 0.5) * 2;
                predictedData.push({
                    time: new Date(new Date(historicalData[historicalData.length - 1].time).getTime() + i * 3600000).toISOString().slice(0, 19).replace('T', ' '),
                    flow: Math.max(0, baseFlow + noise)
                });
            }

            // 保存数据供导出使用
            currentHistoricalData = historicalData;
            currentPredictionData = predictedData;
            currentWatershed = watershed;

            // 创建图表
            createPredictionChart(historicalData, predictedData);

            // 显示预测摘要
            showPredictionSummary(watershed, historicalData, predictedData);
        }

        function createPredictionChart(historicalData, predictedData) {
            const ctx = document.getElementById('predictionChart').getContext('2d');

            if (currentChart) {
                currentChart.destroy();
            }

            const allTimes = [
                ...historicalData.map(d => d.time),
                ...predictedData.map(d => d.time)
            ];

            currentChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: allTimes,
                    datasets: [
                        {
                            label: 'Historical Flow (Observed)',
                            data: [
                                ...historicalData.map(d => d.flow),
                                ...new Array(predictedData.length).fill(null)
                            ],
                            borderColor: '#4caf50',
                            backgroundColor: 'rgba(76, 175, 80, 0.1)',
                            borderWidth: 3,
                            fill: false,
                            tension: 0.1
                        },
                        {
                            label: 'Predicted Flow',
                            data: [
                                ...new Array(historicalData.length).fill(null),
                                ...predictedData.map(d => d.flow)
                            ],
                            borderColor: '#f44336',
                            backgroundColor: 'rgba(244, 67, 54, 0.1)',
                            borderWidth: 3,
                            fill: false,
                            tension: 0.1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Flow Prediction Results',
                            font: {
                                size: 16,
                                weight: 'bold'
                            }
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Time'
                            },
                            ticks: {
                                maxTicksLimit: 10
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Flow (m³/s)'
                            },
                            beginAtZero: true
                        }
                    },
                    elements: {
                        point: {
                            radius: 2
                        }
                    }
                }
            });
        }

        function showPredictionSummary(watershed, historicalData, predictedData) {
            const summaryDiv = document.getElementById('predictionSummary');

            const avgHistorical = historicalData.reduce((sum, d) => sum + d.flow, 0) / historicalData.length;
            const avgPredicted = predictedData.reduce((sum, d) => sum + d.flow, 0) / predictedData.length;
            const maxPredicted = Math.max(...predictedData.map(d => d.flow));
            const minPredicted = Math.min(...predictedData.map(d => d.flow));

            summaryDiv.innerHTML = `
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #667eea;">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">📊 Prediction Summary</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <strong>Watershed:</strong> ${watershed}<br>
                            <strong>Historical Window:</strong> ${historicalData.length} hours<br>
                            <strong>Prediction Window:</strong> ${predictedData.length} hours<br>
                            <strong>Average Historical Flow:</strong> ${avgHistorical.toFixed(2)} m³/s
                        </div>
                        <div>
                            <strong>Average Predicted Flow:</strong> ${avgPredicted.toFixed(2)} m³/s<br>
                            <strong>Maximum Predicted Flow:</strong> ${maxPredicted.toFixed(2)} m³/s<br>
                            <strong>Minimum Predicted Flow:</strong> ${minPredicted.toFixed(2)} m³/s<br>
                            <strong>Flow Change:</strong> ${((avgPredicted - avgHistorical) / avgHistorical * 100).toFixed(1)}%
                        </div>
                    </div>
                </div>
            `;
        }

        // 导出预测数据为CSV格式
        function exportPredictionData(format) {
            if (!currentHistoricalData || !currentPredictionData) {
                alert('没有可导出的预测数据，请先执行流量预测');
                return;
            }

            if (format === 'csv') {
                exportToCSV();
            } else if (format === 'excel') {
                exportToExcel();
            }
        }

        function exportToCSV() {
            // 创建CSV内容
            let csvContent = 'Time,Data_Type,Flow_m3_s,Watershed,Notes\n';

            // 添加历史数据
            currentHistoricalData.forEach(item => {
                csvContent += `${item.time},Historical,${item.flow.toFixed(3)},${currentWatershed},Observed flow data\n`;
            });

            // 添加预测数据
            currentPredictionData.forEach(item => {
                csvContent += `${item.time},Predicted,${item.flow.toFixed(3)},${currentWatershed},Predicted flow data\n`;
            });

            // 创建下载链接
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `flow_prediction_${currentWatershed}_${new Date().toISOString().slice(0, 10)}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function exportToExcel() {
            // 创建工作簿
            const wb = XLSX.utils.book_new();

            // 准备数据
            const data = [
                ['Time', 'Data Type', 'Flow (m³/s)', 'Watershed', 'Notes']
            ];

            // 添加历史数据
            currentHistoricalData.forEach(item => {
                data.push([item.time, 'Historical', parseFloat(item.flow.toFixed(3)), currentWatershed, 'Observed flow data']);
            });

            // 添加预测数据
            currentPredictionData.forEach(item => {
                data.push([item.time, 'Predicted', parseFloat(item.flow.toFixed(3)), currentWatershed, 'Predicted flow data']);
            });

            // 创建工作表
            const ws = XLSX.utils.aoa_to_sheet(data);

            // 设置列宽
            ws['!cols'] = [
                { wch: 20 }, // Time
                { wch: 12 }, // Data Type
                { wch: 15 }, // Flow
                { wch: 12 }, // Watershed
                { wch: 25 }  // Notes
            ];

            // 添加工作表到工作簿
            XLSX.utils.book_append_sheet(wb, ws, 'Flow Prediction');

            // 创建摘要工作表
            const summaryData = [
                ['Flow Prediction Summary'],
                [''],
                ['Parameter', 'Value'],
                ['Watershed', currentWatershed],
                ['Historical Window (hours)', currentHistoricalData.length],
                ['Prediction Window (hours)', currentPredictionData.length],
                ['Average Historical Flow (m³/s)', (currentHistoricalData.reduce((sum, d) => sum + d.flow, 0) / currentHistoricalData.length).toFixed(3)],
                ['Average Predicted Flow (m³/s)', (currentPredictionData.reduce((sum, d) => sum + d.flow, 0) / currentPredictionData.length).toFixed(3)],
                ['Maximum Predicted Flow (m³/s)', Math.max(...currentPredictionData.map(d => d.flow)).toFixed(3)],
                ['Minimum Predicted Flow (m³/s)', Math.min(...currentPredictionData.map(d => d.flow)).toFixed(3)],
                ['Export Date', new Date().toISOString().slice(0, 19).replace('T', ' ')]
            ];

            const summaryWs = XLSX.utils.aoa_to_sheet(summaryData);
            summaryWs['!cols'] = [{ wch: 30 }, { wch: 20 }];
            XLSX.utils.book_append_sheet(wb, summaryWs, 'Summary');

            // 下载文件
            XLSX.writeFile(wb, `flow_prediction_${currentWatershed}_${new Date().toISOString().slice(0, 10)}.xlsx`);
        }

        // 导出图表为图片
        function exportChartImage() {
            if (!currentChart) {
                alert('没有可导出的图表，请先执行流量预测');
                return;
            }

            // 创建下载链接
            const link = document.createElement('a');
            link.download = `flow_prediction_chart_${currentWatershed}_${new Date().toISOString().slice(0, 10)}.png`;
            link.href = currentChart.toBase64Image();
            link.click();
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadFusionData();
        });
    </script>
</body>
</html>
