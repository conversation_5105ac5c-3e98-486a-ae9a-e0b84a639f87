alias: null
batch_size: 32
d_ff: 2048
dataloader_kwargs: null
drop_last_loader: false
dropout: 0.1
e_layers: 2
early_stop_patience_steps: -1
exclude_insample_y: false
factor: 1
futr_exog_list:
- rainfall
h: 12
h_train: 1
hidden_size: 512
hist_exog_list: null
inference_input_size: 128
inference_windows_batch_size: 32
input_size: 128
learning_rate: 0.001
loss: !!python/object:neuralforecast.losses.pytorch.RMSE
  _backward_hooks: !!python/object/apply:collections.OrderedDict
  - []
  _backward_pre_hooks: !!python/object/apply:collections.OrderedDict
  - []
  _buffers: {}
  _forward_hooks: !!python/object/apply:collections.OrderedDict
  - []
  _forward_hooks_always_called: !!python/object/apply:collections.OrderedDict
  - []
  _forward_hooks_with_kwargs: !!python/object/apply:collections.OrderedDict
  - []
  _forward_pre_hooks: !!python/object/apply:collections.OrderedDict
  - []
  _forward_pre_hooks_with_kwargs: !!python/object/apply:collections.OrderedDict
  - []
  _is_full_backward_hook: null
  _load_state_dict_post_hooks: !!python/object/apply:collections.OrderedDict
  - []
  _load_state_dict_pre_hooks: !!python/object/apply:collections.OrderedDict
  - []
  _modules: {}
  _non_persistent_buffers_set: !!set {}
  _parameters: {}
  _state_dict_hooks: !!python/object/apply:collections.OrderedDict
  - []
  _state_dict_pre_hooks: !!python/object/apply:collections.OrderedDict
  - []
  horizon_weight: null
  is_distribution_output: false
  output_names:
  - ''
  outputsize_multiplier: 1
  training: true
lr_scheduler: null
lr_scheduler_kwargs: null
max_steps: 1000
n_heads: 8
n_samples: 100
n_series: 1
num_lr_decays: -1
optimizer: null
optimizer_kwargs: null
patch_len: 16
random_seed: 42
scaler_type: identity
start_padding_enabled: false
stat_exog_list: null
step_size: 1
use_norm: true
val_check_steps: 100
valid_batch_size: null
valid_loss: null
windows_batch_size: 256
