#!/usr/bin/env python3
"""
查看生成的可视化图片
"""

import os
import subprocess
from pathlib import Path

def open_visualizations():
    """打开所有生成的可视化图片"""
    
    visualization_files = [
        "China_Product/lianghe_clipping_visualization.png",
        "China_Product/yantang_clipping_visualization.png", 
        "China_Product/yinhe_clipping_visualization.png",
        "China_Product/watersheds_clipping_comparison.png"
    ]
    
    print("=== 流域裁剪可视化结果 ===\n")
    
    for i, file_path in enumerate(visualization_files, 1):
        full_path = Path(file_path)
        if full_path.exists():
            file_size = full_path.stat().st_size / 1024  # KB
            print(f"{i}. {full_path.name}")
            print(f"   文件大小: {file_size:.1f} KB")
            print(f"   路径: {full_path}")
            
            # 尝试打开图片
            try:
                if os.name == 'posix':  # Linux/Mac
                    subprocess.run(['xdg-open', str(full_path)], check=False)
                elif os.name == 'nt':  # Windows
                    subprocess.run(['start', str(full_path)], shell=True, check=False)
                print(f"   状态: 已尝试打开")
            except Exception as e:
                print(f"   状态: 无法打开 - {e}")
            
            print()
        else:
            print(f"{i}. {file_path} - 文件不存在")
            print()

def show_file_info():
    """显示文件详细信息"""
    
    print("=== 可视化文件详细信息 ===\n")
    
    files_info = {
        "lianghe_clipping_visualization.png": "梁河流域裁剪可视化 - 显示原始栅格、流域边界、裁剪结果和保留的栅格点",
        "yantang_clipping_visualization.png": "盐塘流域裁剪可视化 - 显示原始栅格、流域边界、裁剪结果和保留的栅格点", 
        "yinhe_clipping_visualization.png": "银河流域裁剪可视化 - 显示原始栅格、流域边界、裁剪结果和保留的栅格点",
        "watersheds_clipping_comparison.png": "三个流域对比图 - 并排显示所有流域的裁剪前后对比"
    }
    
    for filename, description in files_info.items():
        file_path = Path(f"China_Product/{filename}")
        print(f"📊 {filename}")
        print(f"   描述: {description}")
        if file_path.exists():
            file_size = file_path.stat().st_size / 1024
            print(f"   大小: {file_size:.1f} KB")
            print(f"   状态: ✅ 存在")
        else:
            print(f"   状态: ❌ 不存在")
        print()

if __name__ == "__main__":
    show_file_info()
    
    # 询问是否打开图片
    try:
        user_input = input("是否尝试打开所有可视化图片？(y/n): ")
        if user_input.lower() in ['y', 'yes', '是']:
            open_visualizations()
        else:
            print("跳过打开图片。")
    except KeyboardInterrupt:
        print("\n用户取消操作。")
    except:
        print("无法获取用户输入，跳过打开图片。")
