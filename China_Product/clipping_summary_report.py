#!/usr/bin/env python3
"""
生成流域裁剪结果的汇总报告
"""

import os
import pandas as pd
from pathlib import Path

def analyze_clipping_results():
    """分析裁剪结果并生成报告"""
    
    print("=== 流域边界裁剪结果汇总报告 ===\n")
    
    # 从终端输出中提取的统计信息
    watersheds_stats = {
        'lianghe': {
            'original_shape': (182, 199),
            'clipped_shape': (182, 199),
            'valid_points': 504,
            'data_range': (0.0000, 0.0800),
            'longitude_bounds': (107.7123, 107.8868),
            'latitude_bounds': (30.2237, 30.3793)
        },
        'yantang': {
            'original_shape': (182, 199),
            'clipped_shape': (182, 199),
            'valid_points': 1584,
            'data_range': (0.0000, 0.1100),
            'longitude_bounds': (106.9546, 107.3279),
            'latitude_bounds': (28.9429, 29.2338)
        },
        'yinhe': {
            'original_shape': (182, 199),
            'clipped_shape': (182, 199),
            'valid_points': 144,
            'data_range': (0.0000, 0.1100),
            'longitude_bounds': (107.8380, 107.9329),
            'latitude_bounds': (30.5846, 30.6979)
        }
    }
    
    # 计算总的原始栅格点数
    total_original_points = 182 * 199
    
    print("1. 基本信息")
    print(f"   - 原始GRIB2栅格尺寸: {182} × {199} = {total_original_points:,} 个栅格点")
    print(f"   - 数据变量: 'unknown' (降水量)")
    print(f"   - 数据维度: (step, latitude, longitude)")
    print(f"   - 时间步数: 72")
    print()
    
    print("2. 各流域裁剪统计")
    print("-" * 80)
    print(f"{'流域名称':<10} {'有效点数':<10} {'保留比例':<10} {'经度范围':<25} {'纬度范围':<25}")
    print("-" * 80)
    
    total_valid_points = 0
    for watershed_name, stats in watersheds_stats.items():
        valid_points = stats['valid_points']
        retention_rate = (valid_points / total_original_points) * 100
        lon_range = f"{stats['longitude_bounds'][0]:.4f}~{stats['longitude_bounds'][1]:.4f}"
        lat_range = f"{stats['latitude_bounds'][0]:.4f}~{stats['latitude_bounds'][1]:.4f}"
        
        print(f"{watershed_name.upper():<10} {valid_points:<10} {retention_rate:<9.3f}% {lon_range:<25} {lat_range:<25}")
        total_valid_points += valid_points
    
    print("-" * 80)
    print(f"{'总计':<10} {total_valid_points:<10} {(total_valid_points/total_original_points)*100:<9.3f}%")
    print()
    
    print("3. 数据值范围分析")
    print("-" * 50)
    print(f"{'流域名称':<10} {'最小值':<10} {'最大值':<10} {'范围':<15}")
    print("-" * 50)
    
    for watershed_name, stats in watersheds_stats.items():
        min_val, max_val = stats['data_range']
        range_val = max_val - min_val
        print(f"{watershed_name.upper():<10} {min_val:<10.4f} {max_val:<10.4f} {range_val:<15.4f}")
    
    print()
    
    print("4. 流域面积对比")
    print("-" * 60)
    print(f"{'流域名称':<10} {'有效栅格点':<12} {'相对大小':<15} {'排序':<10}")
    print("-" * 60)
    
    # 按有效点数排序
    sorted_watersheds = sorted(watersheds_stats.items(), key=lambda x: x[1]['valid_points'], reverse=True)
    
    for i, (watershed_name, stats) in enumerate(sorted_watersheds, 1):
        valid_points = stats['valid_points']
        relative_size = valid_points / sorted_watersheds[0][1]['valid_points']
        print(f"{watershed_name.upper():<10} {valid_points:<12} {relative_size:<14.3f} {'第' + str(i) + '位':<10}")
    
    print()
    
    print("5. 裁剪效果评估")
    print("-" * 40)
    
    # 计算裁剪效率
    total_retention = (total_valid_points / (total_original_points * 3)) * 100
    print(f"   - 总体数据保留率: {total_retention:.2f}%")
    print(f"   - 最大流域(yantang): {watersheds_stats['yantang']['valid_points']} 个有效点")
    print(f"   - 最小流域(yinhe): {watersheds_stats['yinhe']['valid_points']} 个有效点")
    print(f"   - 流域大小比例: {watersheds_stats['yantang']['valid_points']/watersheds_stats['yinhe']['valid_points']:.1f}:1")
    
    print()
    
    print("6. 生成的可视化文件")
    print("-" * 40)
    visualization_files = [
        "lianghe_clipping_visualization.png",
        "yantang_clipping_visualization.png", 
        "yinhe_clipping_visualization.png",
        "watersheds_clipping_comparison.png"
    ]
    
    for i, filename in enumerate(visualization_files, 1):
        file_path = Path(f"China_Product/{filename}")
        if file_path.exists():
            file_size = file_path.stat().st_size / 1024  # KB
            print(f"   {i}. {filename} ({file_size:.1f} KB)")
        else:
            print(f"   {i}. {filename} (文件不存在)")
    
    print()
    
    print("7. 技术说明")
    print("-" * 40)
    print("   - 裁剪方法: 使用shapefile边界进行栅格裁剪")
    print("   - 保留策略: 保留与流域边界相交的所有栅格点")
    print("   - 坐标系统: EPSG:4326 (WGS84)")
    print("   - 栅格分辨率: 约0.05°×0.05°")
    print("   - 数据格式: GRIB2 → CSV")
    
    print()
    print("=== 报告生成完成 ===")

def create_summary_csv():
    """创建CSV格式的汇总表"""
    
    data = {
        '流域名称': ['LIANGHE', 'YANTANG', 'YINHE'],
        '有效栅格点数': [504, 1584, 144],
        '数据保留比例(%)': [1.394, 4.383, 0.398],
        '经度最小值': [107.7123, 106.9546, 107.8380],
        '经度最大值': [107.8868, 107.3279, 107.9329],
        '纬度最小值': [30.2237, 28.9429, 30.5846],
        '纬度最大值': [30.3793, 29.2338, 30.6979],
        '降水最小值': [0.0000, 0.0000, 0.0000],
        '降水最大值': [0.0800, 0.1100, 0.1100]
    }
    
    df = pd.DataFrame(data)
    output_file = "China_Product/watershed_clipping_summary.csv"
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"汇总表已保存到: {output_file}")

if __name__ == "__main__":
    analyze_clipping_results()
    print()
    create_summary_csv()
