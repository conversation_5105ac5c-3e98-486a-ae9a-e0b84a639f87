#!/usr/bin/env python3
"""
可视化流域边界裁剪结果
展示原始栅格数据、流域边界和裁剪后保留的栅格点
"""

import os
import xarray as xr
import geopandas as gpd
import rioxarray as rxr
from shapely.geometry import mapping
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_and_prepare_data(grib_file, shapefile_path):
    """
    加载GRIB2文件和shapefile数据
    
    Parameters:
    -----------
    grib_file : str
        GRIB2文件路径
    shapefile_path : str
        shapefile文件路径
        
    Returns:
    --------
    tuple : (原始数据集, 流域边界, 裁剪后数据集)
    """
    # 1. 读取GRIB2文件
    ds = xr.open_dataset(grib_file, engine='cfgrib')
    ds = ds.rio.write_crs("EPSG:4326")
    
    # 2. 读取shapefile
    gdf = gpd.read_file(shapefile_path)
    if gdf.crs is None:
        gdf = gdf.set_crs("EPSG:4326")
    elif gdf.crs.to_string() != "EPSG:4326":
        gdf = gdf.to_crs("EPSG:4326")
    
    # 3. 进行裁剪
    clipped_ds = ds.rio.clip(gdf.geometry.apply(mapping), gdf.crs, drop=False)
    
    return ds, gdf, clipped_ds

def create_visualization(original_ds, watershed_gdf, clipped_ds, watershed_name, save_path=None):
    """
    创建可视化图表

    Parameters:
    -----------
    original_ds : xarray.Dataset
        原始数据集
    watershed_gdf : geopandas.GeoDataFrame
        流域边界
    clipped_ds : xarray.Dataset
        裁剪后数据集
    watershed_name : str
        流域名称
    save_path : str, optional
        保存路径
    """
    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle(f'{watershed_name.upper()} Watershed Clipping Visualization', fontsize=16, fontweight='bold')

    # 获取数据变量名（通常是'unknown'或'tp'）
    data_var = list(original_ds.data_vars)[0]

    # 检查数据维度并选择合适的索引方式
    data_dims = original_ds[data_var].dims
    print(f"Data variable '{data_var}' dimensions: {data_dims}")

    # 根据实际维度选择数据
    if 'time' in data_dims and 'step' in data_dims:
        original_data = original_ds[data_var].isel(time=0, step=0)
        clipped_data = clipped_ds[data_var].isel(time=0, step=0)
    elif 'step' in data_dims:
        original_data = original_ds[data_var].isel(step=0)
        clipped_data = clipped_ds[data_var].isel(step=0)
    else:
        original_data = original_ds[data_var]
        clipped_data = clipped_ds[data_var]

    # 1. 原始栅格数据
    ax1 = axes[0, 0]
    original_data.plot(ax=ax1, cmap='Blues', add_colorbar=True)
    watershed_gdf.boundary.plot(ax=ax1, color='red', linewidth=2)
    ax1.set_title('Original Grid Data with Watershed Boundary', fontweight='bold')
    ax1.set_xlabel('Longitude')
    ax1.set_ylabel('Latitude')
    ax1.grid(True, alpha=0.3)
    
    # 2. 流域边界详细图
    ax2 = axes[0, 1]
    watershed_gdf.plot(ax=ax2, facecolor='lightblue', edgecolor='red', linewidth=2, alpha=0.7)
    ax2.set_title('Watershed Boundary Detail', fontweight='bold')
    ax2.set_xlabel('Longitude')
    ax2.set_ylabel('Latitude')
    ax2.grid(True, alpha=0.3)
    
    # 3. 裁剪后的栅格数据
    ax3 = axes[1, 0]
    clipped_data.plot(ax=ax3, cmap='Blues', add_colorbar=True)
    watershed_gdf.boundary.plot(ax=ax3, color='red', linewidth=2)
    ax3.set_title('Clipped Grid Data', fontweight='bold')
    ax3.set_xlabel('Longitude')
    ax3.set_ylabel('Latitude')
    ax3.grid(True, alpha=0.3)

    # 4. 保留的栅格点分布
    ax4 = axes[1, 1]

    # 转换为DataFrame获取有效点
    clipped_df = clipped_ds.to_dataframe().reset_index().dropna()

    # 绘制保留的栅格点
    scatter = ax4.scatter(clipped_df['longitude'], clipped_df['latitude'],
                         c=clipped_df[data_var], cmap='Blues', s=20, alpha=0.8)
    watershed_gdf.boundary.plot(ax=ax4, color='red', linewidth=2)

    # 添加颜色条
    plt.colorbar(scatter, ax=ax4, label=f'{data_var} value')

    ax4.set_title(f'Retained Grid Points (Total: {len(clipped_df)})', fontweight='bold')
    ax4.set_xlabel('Longitude')
    ax4.set_ylabel('Latitude')
    ax4.grid(True, alpha=0.3)

    # 调整布局
    plt.tight_layout()

    # 保存图片
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Visualization saved to: {save_path}")

    plt.show()

    # 打印统计信息
    print(f"\n=== {watershed_name.upper()} Clipping Statistics ===")
    print(f"Original grid shape: {original_data.shape}")
    print(f"Clipped grid shape: {clipped_data.shape}")
    print(f"Valid points after clipping: {len(clipped_df)}")
    print(f"Data range: {clipped_df[data_var].min():.4f} to {clipped_df[data_var].max():.4f}")
    print(f"Watershed area bounds:")
    print(f"  Longitude: {watershed_gdf.bounds.minx.iloc[0]:.4f} to {watershed_gdf.bounds.maxx.iloc[0]:.4f}")
    print(f"  Latitude: {watershed_gdf.bounds.miny.iloc[0]:.4f} to {watershed_gdf.bounds.maxy.iloc[0]:.4f}")

def create_comparison_plot(watersheds_data, save_path=None):
    """
    创建多个流域的对比图
    
    Parameters:
    -----------
    watersheds_data : dict
        包含多个流域数据的字典
    save_path : str, optional
        保存路径
    """
    n_watersheds = len(watersheds_data)
    fig, axes = plt.subplots(2, n_watersheds, figsize=(6*n_watersheds, 10))
    
    if n_watersheds == 1:
        axes = axes.reshape(2, 1)
    
    fig.suptitle('Watershed Clipping Comparison', fontsize=16, fontweight='bold')
    
    for i, (watershed_name, data) in enumerate(watersheds_data.items()):
        original_ds, watershed_gdf, clipped_ds = data
        data_var = list(original_ds.data_vars)[0]

        # 检查数据维度并选择合适的索引方式
        data_dims = original_ds[data_var].dims

        # 根据实际维度选择数据
        if 'time' in data_dims and 'step' in data_dims:
            original_data = original_ds[data_var].isel(time=0, step=0)
            clipped_data = clipped_ds[data_var].isel(time=0, step=0)
        elif 'step' in data_dims:
            original_data = original_ds[data_var].isel(step=0)
            clipped_data = clipped_ds[data_var].isel(step=0)
        else:
            original_data = original_ds[data_var]
            clipped_data = clipped_ds[data_var]

        # 上排：原始数据 + 边界
        ax_top = axes[0, i]
        original_data.plot(ax=ax_top, cmap='Blues', add_colorbar=True)
        watershed_gdf.boundary.plot(ax=ax_top, color='red', linewidth=2)
        ax_top.set_title(f'{watershed_name.upper()}\nOriginal + Boundary', fontweight='bold')
        ax_top.grid(True, alpha=0.3)

        # 下排：裁剪后数据
        ax_bottom = axes[1, i]
        clipped_data.plot(ax=ax_bottom, cmap='Blues', add_colorbar=True)
        watershed_gdf.boundary.plot(ax=ax_bottom, color='red', linewidth=2)
        ax_bottom.set_title(f'{watershed_name.upper()}\nClipped Data', fontweight='bold')
        ax_bottom.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Comparison plot saved to: {save_path}")
    
    plt.show()

def inspect_grib_structure(grib_file):
    """检查GRIB2文件结构"""
    print(f"Inspecting GRIB2 file structure: {grib_file}")

    ds = xr.open_dataset(grib_file, engine='cfgrib')
    print(f"Dataset dimensions: {ds.dims}")
    print(f"Dataset coordinates: {list(ds.coords)}")
    print(f"Dataset variables: {list(ds.data_vars)}")
    print(f"Dataset shape: {ds.dims}")

    # 打印每个变量的详细信息
    for var_name in ds.data_vars:
        var = ds[var_name]
        print(f"\nVariable '{var_name}':")
        print(f"  Dimensions: {var.dims}")
        print(f"  Shape: {var.shape}")
        print(f"  Data type: {var.dtype}")

    ds.close()
    return ds

def main():
    """主函数"""
    # 定义流域和shapefile路径
    watersheds = {
        'lianghe': '/home/<USER>/Flood_flow_prediction/boundary/lianghe.shp',
        'yantang': '/home/<USER>/Flood_flow_prediction/boundary/yantang.shp',
        'yinhe': '/home/<USER>/Flood_flow_prediction/boundary/yinhe.shp'
    }

    # 选择一个示例GRIB2文件进行可视化
    # 这里使用2025_final中的第一个可用文件
    source_dir = Path("/home/<USER>/Flood_flow_prediction/China_Product/2025_final")

    # 查找第一个包含GRB2文件的日期文件夹
    grib_file = None
    for date_folder in sorted(source_dir.iterdir()):
        if date_folder.is_dir():
            grb2_files = list(date_folder.glob("*.GRB2"))
            if grb2_files:
                grib_file = str(grb2_files[0])
                print(f"Using GRIB2 file: {grib_file}")
                break

    if not grib_file:
        print("Error: No GRIB2 files found in the source directory!")
        return

    # 首先检查GRIB2文件结构
    try:
        inspect_grib_structure(grib_file)

        # 为每个流域创建可视化
        watersheds_data = {}

        for watershed_name, shapefile_path in watersheds.items():
            print(f"\nProcessing {watershed_name} watershed...")

            try:
                # 加载数据
                original_ds, watershed_gdf, clipped_ds = load_and_prepare_data(grib_file, shapefile_path)
                watersheds_data[watershed_name] = (original_ds, watershed_gdf, clipped_ds)

                # 创建单独的可视化
                save_path = f"China_Product/{watershed_name}_clipping_visualization.png"
                create_visualization(original_ds, watershed_gdf, clipped_ds, watershed_name, save_path)

            except Exception as e:
                print(f"Error processing {watershed_name}: {e}")
                import traceback
                traceback.print_exc()
                continue

        # 创建对比图
        if watersheds_data:
            comparison_save_path = "China_Product/watersheds_clipping_comparison.png"
            create_comparison_plot(watersheds_data, comparison_save_path)

            print(f"\n=== Summary ===")
            print(f"Successfully processed {len(watersheds_data)} watersheds")
            print(f"Individual visualizations saved as: *_clipping_visualization.png")
            print(f"Comparison plot saved as: watersheds_clipping_comparison.png")
        else:
            print("No watersheds were successfully processed.")

    except Exception as e:
        print(f"Error in main function: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
