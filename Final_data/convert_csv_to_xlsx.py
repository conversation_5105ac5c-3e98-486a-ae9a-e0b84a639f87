#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将三个流域的最终CSV文件转换为XLSX格式
"""

import pandas as pd
import os
from datetime import datetime

def convert_csv_to_xlsx(csv_file, xlsx_file):
    """
    将CSV文件转换为XLSX格式
    
    Args:
        csv_file: 输入的CSV文件路径
        xlsx_file: 输出的XLSX文件路径
    """
    try:
        print(f"正在转换 {csv_file} -> {xlsx_file}")
        
        # 读取CSV文件
        df = pd.read_csv(csv_file)
        
        # 转换时间列为datetime格式
        if 'time' in df.columns:
            df['time'] = pd.to_datetime(df['time'])
        
        # 保存为XLSX格式
        with pd.ExcelWriter(xlsx_file, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='数据')
            
            # 获取工作表对象进行格式化
            worksheet = writer.sheets['数据']
            
            # 设置列宽
            worksheet.column_dimensions['A'].width = 20  # time列
            worksheet.column_dimensions['B'].width = 12  # 001列
            worksheet.column_dimensions['C'].width = 12  # 002列
            worksheet.column_dimensions['D'].width = 12  # 003列
            worksheet.column_dimensions['E'].width = 12  # tp列
            worksheet.column_dimensions['F'].width = 12  # flow列
            
            # 设置表头格式
            from openpyxl.styles import Font, PatternFill, Alignment
            
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center")
            
            # 应用表头格式
            for col in range(1, len(df.columns) + 1):
                cell = worksheet.cell(row=1, column=col)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
            
            # 设置数据格式
            data_alignment = Alignment(horizontal="center", vertical="center")
            
            # 为数据行设置对齐方式
            for row in range(2, len(df) + 2):
                for col in range(1, len(df.columns) + 1):
                    cell = worksheet.cell(row=row, column=col)
                    cell.alignment = data_alignment
                    
                    # 为数值列设置数字格式
                    if col > 1:  # 除了时间列外的其他列
                        if col == 2 or col == 3 or col == 4:  # 001, 002, 003列
                            cell.number_format = '0.000000'
                        elif col == 5:  # tp列
                            cell.number_format = '0.000'
                        elif col == 6:  # flow列
                            cell.number_format = '0.000'
        
        print(f"  转换完成！文件大小: {os.path.getsize(xlsx_file) / 1024:.1f} KB")
        return True
        
    except Exception as e:
        print(f"转换 {csv_file} 时出错: {str(e)}")
        return False

def main():
    """主函数"""
    print("开始将CSV文件转换为XLSX格式...")
    print("=" * 60)
    
    # 流域列表
    watersheds = ['lianghe', 'yantang', 'yinhe']
    
    success_count = 0
    
    for watershed in watersheds:
        csv_file = f"Final_data/{watershed}_final.csv"
        xlsx_file = f"Final_data/{watershed}_final.xlsx"
        
        # 检查CSV文件是否存在
        if not os.path.exists(csv_file):
            print(f"错误：文件 {csv_file} 不存在")
            continue
        
        # 转换文件
        if convert_csv_to_xlsx(csv_file, xlsx_file):
            success_count += 1
        
        print("-" * 60)
    
    print(f"转换完成！成功转换 {success_count}/{len(watersheds)} 个文件")
    
    if success_count == len(watersheds):
        print("所有文件转换成功！")
        print("\n生成的XLSX文件:")
        for watershed in watersheds:
            xlsx_file = f"Final_data/{watershed}_final.xlsx"
            if os.path.exists(xlsx_file):
                file_size = os.path.getsize(xlsx_file) / 1024  # KB
                print(f"  - {xlsx_file} ({file_size:.1f} KB)")
    else:
        print("部分文件转换失败，请检查错误信息。")

if __name__ == "__main__":
    main()
