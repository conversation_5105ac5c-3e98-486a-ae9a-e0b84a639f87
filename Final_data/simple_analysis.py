import pandas as pd
import numpy as np
from scipy.stats import pearsonr

# Load data files
files = ['lianghe_final.csv', 'yantang_final.csv', 'yinhe_final.csv']

for file in files:
    print(f"\n{'='*80}")
    watershed = file.replace('_final.csv', '').upper()
    print(f"Analysis for {watershed} Watershed")
    print(f"{'='*80}")
    
    # Load data
    df = pd.read_csv(file)
    print(f"Data shape: {df.shape}")
    
    # Statistical description
    print(f"\nStatistical Description:")
    print(f"{'Column':<8} {'Mean':<12} {'Std Dev':<12} {'Min':<12} {'Max':<12} {'Non-Zero':<10} {'Total':<8}")
    print(f"{'-'*80}")
    
    for col in ['001', '002', '003', 'tp', 'flow']:
        if col in df.columns:
            data = df[col]
            mean_val = data.mean()
            std_val = data.std()
            min_val = data.min()
            max_val = data.max()
            non_zero = (data != 0).sum()
            total = len(data)
            print(f"{col:<8} {mean_val:<12.6f} {std_val:<12.6f} {min_val:<12.6f} {max_val:<12.6f} {non_zero:<10} {total:<8}")
    
    # Correlation analysis
    print(f"\nCorrelation Analysis:")
    print(f"{'Correlation':<15} {'Coefficient':<15} {'P-value':<15} {'Significance':<15}")
    print(f"{'-'*60}")
    
    # Correlations between products and tp
    for product in ['001', '002', '003']:
        if product in df.columns and 'tp' in df.columns:
            corr, p_val = pearsonr(df[product], df['tp'])
            significance = "***" if p_val < 0.001 else "**" if p_val < 0.01 else "*" if p_val < 0.05 else "ns"
            print(f"{product}-tp{'':<8} {corr:<15.6f} {p_val:<15.6e} {significance:<15}")
    
    # Correlation between tp and flow
    if 'tp' in df.columns and 'flow' in df.columns:
        corr, p_val = pearsonr(df['tp'], df['flow'])
        significance = "***" if p_val < 0.001 else "**" if p_val < 0.01 else "*" if p_val < 0.05 else "ns"
        print(f"tp-flow{'':<8} {corr:<15.6f} {p_val:<15.6e} {significance:<15}")

print(f"\n{'='*80}")
print("Significance levels: *** p<0.001, ** p<0.01, * p<0.05, ns = not significant")
print(f"{'='*80}")
