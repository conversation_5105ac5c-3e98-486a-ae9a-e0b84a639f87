# Final_data 统计分析报告

## 概述
本报告对Final_data文件夹中的三个流域（梁河、雁塘、银河）的最终数据文件进行了全面的统计描述和相关性分析。每个数据集包含时间序列数据，涵盖2024年3月28日至2025年5月10日期间的小时数据。

## 数据结构
每个数据文件包含以下变量：
- **time**: 时间戳
- **001**: 预报产品1降水量 (mm)
- **002**: 预报产品2降水量 (mm) 
- **003**: 预报产品3降水量 (mm)
- **tp**: 实测降水量 (mm)
- **flow**: 流量 (m³/s)

## 1. 统计描述分析

### 1.1 两河流域 (LIANGHE)
| 变量 | 均值 | 标准差 | 最小值 | 最大值 | 非零数量 | 总数量 |
|------|------|--------|--------|--------|----------|--------|
| 001 | 0.033371 | 0.112681 | 0.000000 | 2.182034 | 4447 | 9801 |
| 002 | 0.035745 | 0.123588 | 0.000000 | 2.291810 | 5172 | 9801 |
| 003 | 0.832829 | 3.209334 | 0.000000 | 47.121029 | 2117 | 9801 |
| tp | 0.140935 | 0.940224 | 0.000000 | 38.720000 | 1838 | 9801 |
| flow | 2.576462 | 20.472035 | 0.020000 | 776.091000 | 9801 | 9801 |

### 1.2 沿塘流域 (YANTANG)
| 变量 | 均值 | 标准差 | 最小值 | 最大值 | 非零数量 | 总数量 |
|------|------|--------|--------|--------|----------|--------|
| 001 | 0.159290 | 0.465447 | 0.000000 | 7.946124 | 5852 | 9801 |
| 002 | 0.163973 | 0.455972 | 0.000000 | 6.727278 | 7161 | 9801 |
| 003 | 3.374259 | 12.602684 | 0.000000 | 231.466230 | 2824 | 9801 |
| tp | 0.143828 | 0.788413 | 0.000000 | 27.817000 | 2480 | 9801 |
| flow | 4.677160 | 24.776918 | 0.000000 | 564.000000 | 3948 | 9801 |

### 1.3 银河流域 (YINHE)
| 变量 | 均值 | 标准差 | 最小值 | 最大值 | 非零数量 | 总数量 |
|------|------|--------|--------|--------|----------|--------|
| 001 | 0.015692 | 0.053760 | 0.000000 | 0.718887 | 4507 | 9801 |
| 002 | 0.016008 | 0.050924 | 0.000000 | 0.879981 | 5060 | 9801 |
| 003 | 0.323188 | 1.331787 | 0.000000 | 21.375494 | 1966 | 9801 |
| tp | 0.127605 | 0.784356 | 0.000000 | 25.091000 | 1217 | 9801 |
| flow | 0.072037 | 1.201203 | 0.001000 | 55.200000 | 9801 | 9801 |

## 2. 相关性分析

### 2.1 预报产品与实测降水量的相关性

| 流域 | 001-TP | 002-TP | 003-TP |
|------|--------|--------|--------|
| 梁河 | 0.1390*** | 0.1435*** | 0.3772*** |
| 雁塘 | 0.1626*** | 0.2122*** | 0.3291*** |
| 银河 | 0.1938*** | 0.1695*** | 0.3398*** |

### 2.2 实测降水量与流量的相关性

| 流域 | TP-Flow 相关系数 | P值 | 显著性 |
|------|------------------|-----|--------|
| 梁河 | 0.4189 | < 0.001 | *** |
| 雁塘 | 0.1730 | < 0.001 | *** |
| 银河 | 0.2307 | < 0.001 | *** |

**显著性水平**: *** p<0.001, ** p<0.01, * p<0.05

## 3. 预报性能评估 (RMSE & NSE)

### 3.1 均方根误差 (RMSE) 分析

| 流域 | Product 001 | Product 002 | Product 003 |
|------|-------------|-------------|-------------|
| 梁河 | 0.937420 | 0.936438 | 3.063489 |
| 雁塘 | 0.847959 | 0.822971 | 12.780021 |
| 银河 | 0.783722 | 0.785276 | 1.310396 |

**RMSE解释**: 数值越小表示预报精度越高，0为完美预报

### 3.2 Nash-Sutcliffe效率系数 (NSE) 分析

| 流域 | Product 001 | Product 002 | Product 003 |
|------|-------------|-------------|-------------|
| 梁河 | 0.005854 | 0.007936 | -9.617301 |
| 雁塘 | -0.156876 | -0.089697 | -261.784632 |
| 银河 | 0.001514 | -0.002450 | -1.791403 |

**NSE解释**:
- NSE = 1: 完美预报
- NSE = 0: 预报效果等同于使用观测均值
- NSE < 0: 预报效果不如使用观测均值

### 3.3 综合性能排名 (按NSE排序)

| 排名 | 流域 | 产品 | NSE | RMSE | 相关系数 |
|------|------|------|-----|------|----------|
| 1 | 梁河 | 002 | 0.007936 | 0.936438 | 0.143527 |
| 2 | 梁河 | 001 | 0.005854 | 0.937420 | 0.138956 |
| 3 | 银河 | 001 | 0.001514 | 0.783722 | 0.193842 |
| 4 | 银河 | 002 | -0.002450 | 0.785276 | 0.169511 |
| 5 | 雁塘 | 002 | -0.089697 | 0.822971 | 0.212188 |
| 6 | 雁塘 | 001 | -0.156876 | 0.847959 | 0.162640 |

### 3.4 产品平均性能对比

| 产品 | 平均NSE | 平均RMSE | 平均相关系数 |
|------|---------|----------|--------------|
| Product 001 | -0.049836 | 0.856367 | 0.165146 |
| Product 002 | -0.028070 | 0.848228 | 0.175075 |
| Product 003 | -91.064445 | 5.717969 | 0.348709 |

## 4. 数据可用性分析

### 4.1 非零数据百分比

| 流域 | 001 | 002 | 003 | tp |
|------|-----|-----|-----|-----|
| 梁河 | 45.37% | 52.77% | 21.60% | 18.75% |
| 雁塘 | 59.71% | 73.06% | 28.81% | 25.30% |
| 银河 | 45.99% | 51.63% | 20.06% | 12.42% |

## 5. 主要发现

### 5.1 统计特征
1. **雁塘流域**显示出最高的平均降水量和流量值
2. **银河流域**的数据最为稀疏，特别是实测降水量数据
3. **产品003**在所有流域中都显示出最高的变异性（标准差最大）

### 5.2 相关性特征
1. **所有预报产品与实测降水量的相关性都达到统计显著性水平** (p<0.001)
2. **产品003与实测降水量的相关性最强**，在所有流域中相关系数都超过0.32
3. **降水-径流关系显著**，梁河流域的相关性最强(r=0.42)

### 5.3 预报性能特征
1. **产品001和002的NSE表现相对较好**，在梁河和银河流域接近或略高于0
2. **产品003的NSE表现极差**，所有流域NSE均为负值，表明预报效果不如使用观测均值
3. **RMSE方面银河流域表现最好**，梁河流域次之，雁塘流域最差
4. **存在相关性与NSE的矛盾现象**：产品003相关性最高但NSE最差

### 5.4 数据质量
1. **产品002的数据完整性最好**，在雁塘流域达到73.06%的非零率
2. **产品003数据稀疏但质量高**，虽然非零率最低但相关性最强
3. **实测降水量数据稀疏**，银河流域仅有12.42%的非零数据

## 6. 建议

### 6.1 数据融合策略
- **谨慎使用产品003**：虽然相关性最强，但NSE极差，存在系统性偏差
- **优先考虑产品001和002**：NSE表现相对较好，特别是在梁河和银河流域
- **基于NSE的权重融合**：根据各产品在不同流域的NSE表现分配权重
- **分流域融合策略**：梁河流域优先使用产品002，银河流域优先使用产品001

### 6.2 模型开发
- **针对不同流域开发专门的预报模型**，考虑各流域的数据特征差异
- **重点关注产品003的偏差校正**：虽然相关性强但存在严重的系统性误差
- **在模型中考虑数据稀疏性问题**，特别是对银河流域
- **利用降水-径流的强相关性**进行流量预报模型开发

### 6.3 数据质量改进
- **加强实测降水量数据的收集**，特别是银河流域
- **提高产品003的数据覆盖率**，在保持质量的同时增加数据可用性
- **开发产品003的偏差校正方法**，充分利用其高相关性特征

### 6.4 性能评估建议
- **综合考虑多个指标**：不能仅依据相关性评估，需结合NSE、RMSE等
- **关注NSE负值问题**：NSE<0表明预报不如使用观测均值，需要改进
- **建立分级评估体系**：根据NSE值建立预报质量等级标准

## 7. 技术说明

- **分析工具**: Python (pandas, scipy, numpy, matplotlib, seaborn)
- **统计方法**: Pearson相关系数、显著性检验、RMSE、NSE、MAE、Bias
- **评估指标**:
  - **RMSE**: 均方根误差，衡量预报精度
  - **NSE**: Nash-Sutcliffe效率系数，衡量模型性能
  - **MAE**: 平均绝对误差，衡量预报偏差
  - **Bias**: 系统性偏差，正值表示高估，负值表示低估
- **数据期间**: 2024-03-28 09:00 至 2025-05-10 17:00
- **数据频率**: 小时数据
- **样本量**: 每个流域9801个观测值

## 8. 生成文件

1. `Statistical_Analysis_Report.md` - 完整分析报告
2. `enhanced_analysis.py` - 增强分析脚本（含RMSE、NSE）
3. `performance_visualization.py` - 性能可视化脚本
4. `performance_metrics_analysis.png` - 性能指标可视化图表
5. `statistical_analysis_visualization.png` - 统计分析可视化图表
6. `correlation_matrices.png` - 相关性矩阵热图
