import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import pearsonr

# Set style for better plots
plt.style.use('default')
sns.set_palette("husl")

def create_visualizations():
    """Create comprehensive visualizations for the analysis"""
    
    files = ['lianghe_final.csv', 'yantang_final.csv', 'yinhe_final.csv']
    watersheds = ['LIANGHE', 'YANTANG', 'YINHE']
    
    # Load all data
    all_data = {}
    for i, file in enumerate(files):
        watershed = watersheds[i]
        df = pd.read_csv(file)
        all_data[watershed] = df
    
    # Create figure with subplots
    fig = plt.figure(figsize=(20, 16))
    
    # 1. Statistical Summary Heatmap
    plt.subplot(3, 3, 1)
    
    # Prepare data for heatmap
    stats_data = []
    for watershed in watersheds:
        df = all_data[watershed]
        row = []
        for col in ['001', '002', '003', 'tp', 'flow']:
            row.append(df[col].mean())
        stats_data.append(row)
    
    stats_df = pd.DataFrame(stats_data, 
                           index=watersheds, 
                           columns=['Product 001', 'Product 002', 'Product 003', 'Actual TP', 'Flow'])
    
    sns.heatmap(stats_df, annot=True, fmt='.3f', cmap='YlOrRd', cbar_kws={'label': 'Mean Value'})
    plt.title('Mean Values Across Watersheds', fontsize=14, fontweight='bold')
    plt.ylabel('Watershed')
    
    # 2. Correlation Heatmap
    plt.subplot(3, 3, 2)
    
    corr_data = []
    for watershed in watersheds:
        df = all_data[watershed]
        row = []
        for product in ['001', '002', '003']:
            corr, _ = pearsonr(df[product], df['tp'])
            row.append(corr)
        # Add tp-flow correlation
        corr, _ = pearsonr(df['tp'], df['flow'])
        row.append(corr)
        corr_data.append(row)
    
    corr_df = pd.DataFrame(corr_data, 
                          index=watersheds, 
                          columns=['001-TP', '002-TP', '003-TP', 'TP-Flow'])
    
    sns.heatmap(corr_df, annot=True, fmt='.3f', cmap='RdBu_r', center=0, 
                vmin=-1, vmax=1, cbar_kws={'label': 'Correlation Coefficient'})
    plt.title('Correlation Coefficients', fontsize=14, fontweight='bold')
    plt.ylabel('Watershed')
    
    # 3. Data Availability
    plt.subplot(3, 3, 3)
    
    availability_data = []
    for watershed in watersheds:
        df = all_data[watershed]
        row = []
        for col in ['001', '002', '003', 'tp']:
            non_zero_pct = ((df[col] != 0).sum() / len(df)) * 100
            row.append(non_zero_pct)
        availability_data.append(row)
    
    avail_df = pd.DataFrame(availability_data, 
                           index=watersheds, 
                           columns=['Product 001', 'Product 002', 'Product 003', 'Actual TP'])
    
    sns.heatmap(avail_df, annot=True, fmt='.1f', cmap='Greens', 
                cbar_kws={'label': 'Data Availability (%)'})
    plt.title('Data Availability (Non-Zero %)', fontsize=14, fontweight='bold')
    plt.ylabel('Watershed')
    
    # 4-6. Box plots for each watershed
    for i, watershed in enumerate(watersheds):
        plt.subplot(3, 3, 4+i)
        df = all_data[watershed]
        
        # Prepare data for box plot (log scale for better visualization)
        plot_data = []
        labels = []
        for col in ['001', '002', '003', 'tp']:
            non_zero_data = df[df[col] > 0][col]
            if len(non_zero_data) > 0:
                plot_data.append(non_zero_data)
                labels.append(col)
        
        if plot_data:
            plt.boxplot(plot_data, labels=labels)
            plt.yscale('log')
            plt.title(f'{watershed} - Non-Zero Values Distribution', fontsize=12, fontweight='bold')
            plt.ylabel('Value (log scale)')
            plt.xticks(rotation=45)
    
    # 7-9. Scatter plots: Product vs TP for each watershed
    for i, watershed in enumerate(watersheds):
        plt.subplot(3, 3, 7+i)
        df = all_data[watershed]
        
        # Plot all three products vs tp
        colors = ['red', 'blue', 'green']
        products = ['001', '002', '003']
        
        for j, product in enumerate(products):
            # Only plot non-zero values for better visualization
            mask = (df[product] > 0) & (df['tp'] > 0)
            if mask.sum() > 0:
                plt.scatter(df[mask][product], df[mask]['tp'], 
                           alpha=0.6, s=10, color=colors[j], label=f'Product {product}')
        
        plt.xlabel('Product Value')
        plt.ylabel('Actual TP')
        plt.title(f'{watershed} - Products vs Actual TP', fontsize=12, fontweight='bold')
        plt.legend()
        plt.xscale('log')
        plt.yscale('log')
    
    plt.tight_layout()
    plt.savefig('statistical_analysis_visualization.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Create a separate correlation matrix plot
    fig, axes = plt.subplots(1, 3, figsize=(18, 5))
    
    for i, watershed in enumerate(watersheds):
        df = all_data[watershed]
        
        # Calculate correlation matrix for all numeric variables
        corr_matrix = df[['001', '002', '003', 'tp', 'flow']].corr()
        
        sns.heatmap(corr_matrix, annot=True, fmt='.3f', cmap='RdBu_r', center=0,
                   vmin=-1, vmax=1, ax=axes[i], square=True)
        axes[i].set_title(f'{watershed} Correlation Matrix', fontsize=14, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('correlation_matrices.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("Visualizations saved as:")
    print("1. statistical_analysis_visualization.png")
    print("2. correlation_matrices.png")

if __name__ == "__main__":
    create_visualizations()
