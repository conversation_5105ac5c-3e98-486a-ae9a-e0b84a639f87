import pandas as pd
import numpy as np
from scipy.stats import pearsonr
import matplotlib.pyplot as plt
import seaborn as sns

def calculate_rmse(observed, predicted):
    """Calculate Root Mean Square Error"""
    return np.sqrt(np.mean((observed - predicted) ** 2))

def calculate_nse(observed, predicted):
    """Calculate Nash-Sutcliffe Efficiency"""
    numerator = np.sum((observed - predicted) ** 2)
    denominator = np.sum((observed - np.mean(observed)) ** 2)
    if denominator == 0:
        return np.nan
    return 1 - (numerator / denominator)

def calculate_mae(observed, predicted):
    """Calculate Mean Absolute Error"""
    return np.mean(np.abs(observed - predicted))

def calculate_bias(observed, predicted):
    """Calculate Bias (Mean Error)"""
    return np.mean(predicted - observed)

def enhanced_analysis():
    """Enhanced analysis with RMSE, NSE, MAE, and Bias"""
    
    files = ['lianghe_final.csv', 'yantang_final.csv', 'yinhe_final.csv']
    watersheds = ['LIANGHE', 'YANTANG', 'YINHE']
    
    print("="*120)
    print("ENHANCED STATISTICAL ANALYSIS WITH RMSE, NSE, MAE, AND BIAS")
    print("="*120)
    
    # Store results for summary
    all_metrics = {}
    
    for i, file in enumerate(files):
        watershed = watersheds[i]
        df = pd.read_csv(file)
        
        print(f"\n{'='*80}")
        print(f"Analysis for {watershed} Watershed")
        print(f"{'='*80}")
        print(f"Data shape: {df.shape}")
        
        # Basic statistics
        print(f"\nBasic Statistics:")
        print(f"{'Variable':<8} {'Mean':<12} {'Std Dev':<12} {'Min':<12} {'Max':<12} {'Non-Zero':<10}")
        print(f"{'-'*70}")
        
        for col in ['001', '002', '003', 'tp']:
            if col in df.columns:
                data = df[col]
                mean_val = data.mean()
                std_val = data.std()
                min_val = data.min()
                max_val = data.max()
                non_zero = (data != 0).sum()
                print(f"{col:<8} {mean_val:<12.6f} {std_val:<12.6f} {min_val:<12.6f} {max_val:<12.6f} {non_zero:<10}")
        
        # Performance metrics for each product vs tp
        print(f"\nPerformance Metrics (Products vs Actual TP):")
        print(f"{'Product':<8} {'Correlation':<12} {'P-value':<12} {'RMSE':<12} {'NSE':<12} {'MAE':<12} {'Bias':<12}")
        print(f"{'-'*84}")
        
        metrics = {}
        
        for product in ['001', '002', '003']:
            if product in df.columns and 'tp' in df.columns:
                observed = df['tp'].values
                predicted = df[product].values
                
                # Calculate metrics
                corr, p_val = pearsonr(predicted, observed)
                rmse = calculate_rmse(observed, predicted)
                nse = calculate_nse(observed, predicted)
                mae = calculate_mae(observed, predicted)
                bias = calculate_bias(observed, predicted)
                
                metrics[product] = {
                    'correlation': corr,
                    'p_value': p_val,
                    'rmse': rmse,
                    'nse': nse,
                    'mae': mae,
                    'bias': bias
                }
                
                significance = "***" if p_val < 0.001 else "**" if p_val < 0.01 else "*" if p_val < 0.05 else "ns"
                print(f"{product:<8} {corr:<12.6f} {p_val:<12.6e} {rmse:<12.6f} {nse:<12.6f} {mae:<12.6f} {bias:<12.6f}")
        
        all_metrics[watershed] = metrics
        
        # Performance on non-zero data only
        print(f"\nPerformance Metrics (Non-Zero Data Only):")
        print(f"{'Product':<8} {'N_pairs':<10} {'Correlation':<12} {'RMSE':<12} {'NSE':<12} {'MAE':<12} {'Bias':<12}")
        print(f"{'-'*84}")
        
        for product in ['001', '002', '003']:
            if product in df.columns and 'tp' in df.columns:
                # Filter for cases where both values are non-zero
                mask = (df[product] > 0) & (df['tp'] > 0)
                
                if mask.sum() > 10:  # Need at least 10 pairs for meaningful analysis
                    observed = df.loc[mask, 'tp'].values
                    predicted = df.loc[mask, product].values
                    
                    corr, _ = pearsonr(predicted, observed)
                    rmse = calculate_rmse(observed, predicted)
                    nse = calculate_nse(observed, predicted)
                    mae = calculate_mae(observed, predicted)
                    bias = calculate_bias(observed, predicted)
                    
                    print(f"{product:<8} {mask.sum():<10} {corr:<12.6f} {rmse:<12.6f} {nse:<12.6f} {mae:<12.6f} {bias:<12.6f}")
                else:
                    print(f"{product:<8} {mask.sum():<10} {'Insufficient data'}")
    
    # Summary comparison across watersheds
    print(f"\n{'='*120}")
    print("SUMMARY COMPARISON ACROSS WATERSHEDS")
    print(f"{'='*120}")

    # RMSE comparison
    print(f"\nRMSE Comparison:")
    print(f"{'Watershed':<10} {'Product 001':<12} {'Product 002':<12} {'Product 003':<12}")
    print(f"{'-'*50}")

    for watershed in watersheds:
        metrics = all_metrics[watershed]
        rmse_001 = metrics['001']['rmse'] if '001' in metrics else np.nan
        rmse_002 = metrics['002']['rmse'] if '002' in metrics else np.nan
        rmse_003 = metrics['003']['rmse'] if '003' in metrics else np.nan
        print(f"{watershed:<10} {rmse_001:<12.6f} {rmse_002:<12.6f} {rmse_003:<12.6f}")

    # NSE comparison
    print(f"\nNSE Comparison:")
    print(f"{'Watershed':<10} {'Product 001':<12} {'Product 002':<12} {'Product 003':<12}")
    print(f"{'-'*50}")

    for watershed in watersheds:
        metrics = all_metrics[watershed]
        nse_001 = metrics['001']['nse'] if '001' in metrics else np.nan
        nse_002 = metrics['002']['nse'] if '002' in metrics else np.nan
        nse_003 = metrics['003']['nse'] if '003' in metrics else np.nan
        print(f"{watershed:<10} {nse_001:<12.6f} {nse_002:<12.6f} {nse_003:<12.6f}")

    # Performance ranking
    print(f"\nPerformance Ranking by NSE (Higher is Better):")
    print(f"{'Rank':<5} {'Watershed':<10} {'Product':<10} {'NSE':<12} {'RMSE':<12} {'Correlation':<12}")
    print(f"{'-'*65}")

    # Collect all NSE values for ranking
    nse_results = []
    for watershed in watersheds:
        metrics = all_metrics[watershed]
        for product in ['001', '002', '003']:
            if product in metrics:
                nse_results.append({
                    'watershed': watershed,
                    'product': product,
                    'nse': metrics[product]['nse'],
                    'rmse': metrics[product]['rmse'],
                    'correlation': metrics[product]['correlation']
                })

    # Sort by NSE (descending)
    nse_results.sort(key=lambda x: x['nse'], reverse=True)

    for i, result in enumerate(nse_results, 1):
        print(f"{i:<5} {result['watershed']:<10} {result['product']:<10} {result['nse']:<12.6f} "
              f"{result['rmse']:<12.6f} {result['correlation']:<12.6f}")

    print(f"\n{'='*120}")
    print("INTERPRETATION GUIDE:")
    print("• RMSE: Lower values indicate better performance (0 = perfect)")
    print("• NSE: Higher values indicate better performance (1 = perfect, 0 = as good as mean, <0 = worse than mean)")
    print("• Correlation: Higher absolute values indicate stronger linear relationship")
    print("• MAE: Lower values indicate better performance (Mean Absolute Error)")
    print("• Bias: Values closer to 0 indicate less systematic error (+ = overestimation, - = underestimation)")
    print(f"{'='*120}")

    return all_metrics

if __name__ == "__main__":
    metrics = enhanced_analysis()
