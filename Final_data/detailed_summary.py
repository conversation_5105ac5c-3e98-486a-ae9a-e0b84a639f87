import pandas as pd
import numpy as np
from scipy.stats import pearsonr

def create_summary_report():
    """Create a comprehensive summary report"""
    
    files = ['lianghe_final.csv', 'yantang_final.csv', 'yinhe_final.csv']
    watersheds = ['LIANGHE', 'YANTANG', 'YINHE']
    
    print("="*100)
    print("COMPREHENSIVE STATISTICAL ANALYSIS REPORT")
    print("Final_data/*_final.csv Files Analysis")
    print("="*100)
    
    # Summary statistics table
    print("\n1. SUMMARY STATISTICS TABLE")
    print("="*100)
    print(f"{'Watershed':<10} {'Variable':<8} {'Mean':<12} {'Std Dev':<12} {'Min':<12} {'Max':<12} {'Non-Zero':<10} {'Total':<8}")
    print("-"*100)
    
    all_data = {}
    
    for i, file in enumerate(files):
        watershed = watersheds[i]
        df = pd.read_csv(file)
        all_data[watershed] = df
        
        for col in ['001', '002', '003', 'tp', 'flow']:
            if col in df.columns:
                data = df[col]
                mean_val = data.mean()
                std_val = data.std()
                min_val = data.min()
                max_val = data.max()
                non_zero = (data != 0).sum()
                total = len(data)
                print(f"{watershed:<10} {col:<8} {mean_val:<12.6f} {std_val:<12.6f} {min_val:<12.6f} {max_val:<12.6f} {non_zero:<10} {total:<8}")
    
    # Correlation analysis
    print(f"\n2. CORRELATION ANALYSIS")
    print("="*80)
    print(f"{'Watershed':<10} {'Correlation':<15} {'Coefficient':<15} {'P-value':<15} {'Significance':<15}")
    print("-"*80)
    
    correlation_summary = {}
    
    for i, file in enumerate(files):
        watershed = watersheds[i]
        df = pd.read_csv(file)
        correlation_summary[watershed] = {}
        
        # Correlations between products and tp
        for product in ['001', '002', '003']:
            if product in df.columns and 'tp' in df.columns:
                corr, p_val = pearsonr(df[product], df['tp'])
                significance = "***" if p_val < 0.001 else "**" if p_val < 0.01 else "*" if p_val < 0.05 else "ns"
                correlation_summary[watershed][f'{product}_tp'] = {'corr': corr, 'p_val': p_val, 'sig': significance}
                print(f"{watershed:<10} {product}-tp{'':<10} {corr:<15.6f} {p_val:<15.6e} {significance:<15}")
        
        # Correlation between tp and flow
        if 'tp' in df.columns and 'flow' in df.columns:
            corr, p_val = pearsonr(df['tp'], df['flow'])
            significance = "***" if p_val < 0.001 else "**" if p_val < 0.01 else "*" if p_val < 0.05 else "ns"
            correlation_summary[watershed]['tp_flow'] = {'corr': corr, 'p_val': p_val, 'sig': significance}
            print(f"{watershed:<10} tp-flow{'':<10} {corr:<15.6f} {p_val:<15.6e} {significance:<15}")
    
    # Cross-watershed comparison
    print(f"\n3. CROSS-WATERSHED COMPARISON")
    print("="*80)
    
    print(f"\n3.1 Mean Values Comparison")
    print(f"{'Watershed':<10} {'001 Mean':<12} {'002 Mean':<12} {'003 Mean':<12} {'TP Mean':<12} {'Flow Mean':<12}")
    print("-"*70)
    
    for watershed in watersheds:
        df = all_data[watershed]
        means = [df[col].mean() for col in ['001', '002', '003', 'tp', 'flow']]
        print(f"{watershed:<10} {means[0]:<12.6f} {means[1]:<12.6f} {means[2]:<12.6f} {means[3]:<12.6f} {means[4]:<12.6f}")
    
    print(f"\n3.2 Correlation Coefficients Comparison")
    print(f"{'Watershed':<10} {'001-TP':<10} {'002-TP':<10} {'003-TP':<10} {'TP-Flow':<10}")
    print("-"*60)
    
    for watershed in watersheds:
        corrs = correlation_summary[watershed]
        print(f"{watershed:<10} {corrs['001_tp']['corr']:<10.4f} {corrs['002_tp']['corr']:<10.4f} "
              f"{corrs['003_tp']['corr']:<10.4f} {corrs['tp_flow']['corr']:<10.4f}")
    
    # Data availability analysis
    print(f"\n4. DATA AVAILABILITY ANALYSIS")
    print("="*80)
    print(f"{'Watershed':<10} {'Variable':<8} {'Non-Zero %':<12} {'Zero Count':<12} {'Non-Zero Count':<15}")
    print("-"*60)
    
    for watershed in watersheds:
        df = all_data[watershed]
        for col in ['001', '002', '003', 'tp', 'flow']:
            if col in df.columns:
                data = df[col]
                non_zero_count = (data != 0).sum()
                zero_count = (data == 0).sum()
                non_zero_pct = (non_zero_count / len(data)) * 100
                print(f"{watershed:<10} {col:<8} {non_zero_pct:<12.2f} {zero_count:<12} {non_zero_count:<15}")
    
    print(f"\n5. KEY FINDINGS")
    print("="*80)
    print("• All correlations between forecast products (001, 002, 003) and actual rainfall (tp) are statistically significant (p<0.001)")
    print("• Product 003 shows the strongest correlation with actual rainfall across all watersheds")
    print("• TP-Flow correlations are positive and significant, indicating rainfall-runoff relationship")
    print("• YANTANG watershed shows highest mean values for most variables")
    print("• YINHE watershed has the most sparse data (lowest non-zero percentages)")
    print("• Product 003 has the lowest data availability but highest correlation with actual rainfall")
    
    print(f"\nSignificance levels: *** p<0.001, ** p<0.01, * p<0.05, ns = not significant")
    print("="*100)

if __name__ == "__main__":
    create_summary_report()
