import pandas as pd
import numpy as np
import glob
import os
from scipy import stats
from scipy.stats import pearsonr
import matplotlib.pyplot as plt
import seaborn as sns

def load_final_data():
    """Load all *_final.csv files from Final_data folder"""
    data_files = glob.glob('*_final.csv')
    datasets = {}
    
    for file in data_files:
        watershed = file.replace('_final.csv', '')
        df = pd.read_csv(file)
        datasets[watershed] = df
        print(f"Loaded {file}: {df.shape}")
    
    return datasets

def calculate_statistics(df, watershed_name):
    """Calculate descriptive statistics for all columns except time"""
    # Exclude time column
    numeric_cols = [col for col in df.columns if col != 'time']
    
    stats_dict = {}
    
    for col in numeric_cols:
        data = df[col]
        stats_dict[col] = {
            'mean': data.mean(),
            'std': data.std(),
            'min': data.min(),
            'max': data.max(),
            'non_zero_count': (data != 0).sum(),
            'total_count': len(data)
        }
    
    return stats_dict

def calculate_correlations(df):
    """Calculate correlations between products (001, 002, 003) and tp, and between tp and flow"""
    # Exclude time column
    numeric_cols = [col for col in df.columns if col != 'time']
    
    correlations = {}
    p_values = {}
    
    # Correlations between products and tp
    products = ['001', '002', '003']
    for product in products:
        if product in df.columns and 'tp' in df.columns:
            corr, p_val = pearsonr(df[product], df['tp'])
            correlations[f'{product}_tp'] = corr
            p_values[f'{product}_tp'] = p_val
    
    # Correlation between tp and flow
    if 'tp' in df.columns and 'flow' in df.columns:
        corr, p_val = pearsonr(df['tp'], df['flow'])
        correlations['tp_flow'] = corr
        p_values['tp_flow'] = p_val
    
    return correlations, p_values

def print_statistics_table(stats_dict, watershed_name):
    """Print formatted statistics table"""
    print(f"\n{'='*80}")
    print(f"Statistical Description for {watershed_name.upper()} Watershed")
    print(f"{'='*80}")
    
    # Create header
    print(f"{'Column':<8} {'Mean':<12} {'Std Dev':<12} {'Min':<12} {'Max':<12} {'Non-Zero':<10} {'Total':<8}")
    print(f"{'-'*80}")
    
    for col, stats in stats_dict.items():
        print(f"{col:<8} {stats['mean']:<12.6f} {stats['std']:<12.6f} {stats['min']:<12.6f} "
              f"{stats['max']:<12.6f} {stats['non_zero_count']:<10} {stats['total_count']:<8}")

def print_correlation_table(correlations, p_values, watershed_name):
    """Print formatted correlation table"""
    print(f"\n{'='*60}")
    print(f"Correlation Analysis for {watershed_name.upper()} Watershed")
    print(f"{'='*60}")
    
    print(f"{'Correlation':<15} {'Coefficient':<15} {'P-value':<15} {'Significance':<15}")
    print(f"{'-'*60}")
    
    for corr_name, corr_val in correlations.items():
        p_val = p_values[corr_name]
        significance = "***" if p_val < 0.001 else "**" if p_val < 0.01 else "*" if p_val < 0.05 else "ns"
        print(f"{corr_name:<15} {corr_val:<15.6f} {p_val:<15.6e} {significance:<15}")
    
    print("\nSignificance levels: *** p<0.001, ** p<0.01, * p<0.05, ns = not significant")

def main():
    """Main analysis function"""
    print("Statistical Analysis of Final_data/*_final.csv Files")
    print("="*60)
    
    # Load all datasets
    datasets = load_final_data()
    
    if not datasets:
        print("No *_final.csv files found in current directory!")
        return
    
    # Analyze each watershed
    all_stats = {}
    all_correlations = {}
    all_p_values = {}
    
    for watershed, df in datasets.items():
        print(f"\nProcessing {watershed} watershed...")
        
        # Calculate statistics
        stats_dict = calculate_statistics(df, watershed)
        all_stats[watershed] = stats_dict
        
        # Calculate correlations
        correlations, p_values = calculate_correlations(df)
        all_correlations[watershed] = correlations
        all_p_values[watershed] = p_values
        
        # Print results
        print_statistics_table(stats_dict, watershed)
        print_correlation_table(correlations, p_values, watershed)
    
    # Summary across all watersheds
    print(f"\n{'='*80}")
    print("SUMMARY ACROSS ALL WATERSHEDS")
    print(f"{'='*80}")
    
    # Summary statistics table
    print(f"\n{'Watershed':<12} {'001 Mean':<10} {'002 Mean':<10} {'003 Mean':<10} {'TP Mean':<10} {'Flow Mean':<10}")
    print(f"{'-'*70}")
    
    for watershed in all_stats:
        stats = all_stats[watershed]
        print(f"{watershed:<12} {stats['001']['mean']:<10.4f} {stats['002']['mean']:<10.4f} "
              f"{stats['003']['mean']:<10.4f} {stats['tp']['mean']:<10.4f} {stats['flow']['mean']:<10.4f}")
    
    # Summary correlation table
    print(f"\n{'Watershed':<12} {'001-TP':<10} {'002-TP':<10} {'003-TP':<10} {'TP-Flow':<10}")
    print(f"{'-'*60}")
    
    for watershed in all_correlations:
        corrs = all_correlations[watershed]
        print(f"{watershed:<12} {corrs.get('001_tp', 0):<10.4f} {corrs.get('002_tp', 0):<10.4f} "
              f"{corrs.get('003_tp', 0):<10.4f} {corrs.get('tp_flow', 0):<10.4f}")

if __name__ == "__main__":
    main()
