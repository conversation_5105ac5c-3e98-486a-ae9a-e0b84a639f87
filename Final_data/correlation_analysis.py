#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Final_data流域数据相关性分析
对lianghe、yanta<PERSON>、yinhe三个流域的final数据进行相关性分析和可视化
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import pearsonr
import warnings
warnings.filterwarnings('ignore')

# 设置字体
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_watershed_data():
    """加载三个流域的数据"""
    watersheds = ['lianghe', 'yantang', 'yinhe']
    data = {}
    
    for watershed in watersheds:
        file_path = f'{watershed}_final.csv'
        df = pd.read_csv(file_path)
        
        # 转换时间列
        df['time'] = pd.to_datetime(df['time'])
        
        # 提取数值变量（除时间外）
        numeric_vars = ['001', '002', '003', 'tp', 'flow']
        data[watershed] = df[numeric_vars].copy()
        
        print(f"{watershed}流域数据加载完成，形状: {data[watershed].shape}")
        print(f"数据范围: {df['time'].min()} 到 {df['time'].max()}")
        print(f"缺失值统计:\n{data[watershed].isnull().sum()}\n")
    
    return data

def calculate_correlations(data):
    """计算相关性矩阵"""
    correlations = {}
    
    for watershed, df in data.items():
        # 计算Pearson相关系数矩阵
        corr_matrix = df.corr(method='pearson')
        correlations[watershed] = corr_matrix
        
        print(f"\n{watershed}流域相关性矩阵:")
        print(corr_matrix.round(3))
        
        # 找出最强的正相关和负相关（排除自相关）
        corr_values = []
        for i in range(len(corr_matrix.columns)):
            for j in range(i+1, len(corr_matrix.columns)):
                var1 = corr_matrix.columns[i]
                var2 = corr_matrix.columns[j]
                corr_val = corr_matrix.iloc[i, j]
                corr_values.append((var1, var2, corr_val))
        
        # 排序找出最强相关性
        corr_values.sort(key=lambda x: abs(x[2]), reverse=True)
        
        print(f"\n{watershed}流域最强相关性（前5个）:")
        for i, (var1, var2, corr_val) in enumerate(corr_values[:5]):
            print(f"{i+1}. {var1} vs {var2}: {corr_val:.3f}")
    
    return correlations

def plot_correlation_heatmaps(correlations):
    """绘制相关性热力图"""
    fig, axes = plt.subplots(1, 3, figsize=(18, 5))
    fig.suptitle('Correlation Heatmaps for Three Watersheds', fontsize=16, fontweight='bold')

    watersheds = ['lianghe', 'yantang', 'yinhe']
    watershed_names = ['Lianghe', 'Yantang', 'Yinhe']
    
    for i, (watershed, name) in enumerate(zip(watersheds, watershed_names)):
        corr_matrix = correlations[watershed]
        
        # 创建热力图
        sns.heatmap(corr_matrix, 
                   annot=True, 
                   cmap='RdBu_r', 
                   center=0,
                   square=True,
                   fmt='.3f',
                   cbar_kws={'shrink': 0.8},
                   ax=axes[i])
        
        axes[i].set_title(f'{name}', fontsize=14, fontweight='bold')
        axes[i].set_xlabel('')
        axes[i].set_ylabel('')
        
        # 旋转x轴标签
        axes[i].tick_params(axis='x', rotation=45)
        axes[i].tick_params(axis='y', rotation=0)
    
    plt.tight_layout()
    plt.savefig('correlation_heatmaps.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_scatter_matrix(data):
    """绘制散点图矩阵"""
    watersheds = ['lianghe', 'yantang', 'yinhe']
    watershed_names = ['Lianghe Watershed', 'Yantang Watershed', 'Yinhe Watershed']
    
    for watershed, name in zip(watersheds, watershed_names):
        df = data[watershed]
        
        # 创建散点图矩阵
        fig, axes = plt.subplots(5, 5, figsize=(15, 15))
        fig.suptitle(f'{name} - Variable Scatter Plot Matrix', fontsize=16, fontweight='bold')
        
        variables = df.columns
        
        for i, var1 in enumerate(variables):
            for j, var2 in enumerate(variables):
                ax = axes[i, j]
                
                if i == j:
                    # 对角线绘制直方图
                    ax.hist(df[var1], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
                    ax.set_title(f'{var1}', fontsize=10)
                else:
                    # 非对角线绘制散点图
                    ax.scatter(df[var2], df[var1], alpha=0.5, s=1)
                    
                    # 计算相关系数
                    corr, _ = pearsonr(df[var2], df[var1])
                    ax.text(0.05, 0.95, f'r={corr:.3f}', 
                           transform=ax.transAxes, 
                           bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                           fontsize=8)
                
                # 设置标签
                if i == len(variables) - 1:
                    ax.set_xlabel(var2, fontsize=10)
                if j == 0:
                    ax.set_ylabel(var1, fontsize=10)
                
                # 调整刻度标签大小
                ax.tick_params(labelsize=8)
        
        plt.tight_layout()
        plt.savefig(f'{watershed}_scatter_matrix.png', dpi=300, bbox_inches='tight')
        plt.show()

def cross_watershed_correlation(data):
    """计算三个流域间同类变量的相关性"""
    watersheds = ['lianghe', 'yantang', 'yinhe']
    variables = ['001', '002', '003', 'tp', 'flow']
    
    # 创建跨流域相关性矩阵
    cross_corr_results = {}
    
    for var in variables:
        cross_corr_results[var] = pd.DataFrame(index=watersheds, columns=watersheds)
        
        for w1 in watersheds:
            for w2 in watersheds:
                if w1 == w2:
                    cross_corr_results[var].loc[w1, w2] = 1.0
                else:
                    corr, _ = pearsonr(data[w1][var], data[w2][var])
                    cross_corr_results[var].loc[w1, w2] = corr
        
        cross_corr_results[var] = cross_corr_results[var].astype(float)
    
    return cross_corr_results

def plot_cross_watershed_correlations(cross_corr_results):
    """绘制跨流域相关性图"""
    variables = ['001', '002', '003', 'tp', 'flow']
    var_names = ['Product 001', 'Product 002', 'Product 003', 'Actual Rainfall', 'Flow']

    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Cross-Watershed Variable Correlation Analysis', fontsize=16, fontweight='bold')
    
    axes = axes.flatten()
    
    for i, (var, var_name) in enumerate(zip(variables, var_names)):
        if i < len(axes):
            ax = axes[i]
            
            # 绘制热力图
            sns.heatmap(cross_corr_results[var], 
                       annot=True, 
                       cmap='RdBu_r', 
                       center=0,
                       square=True,
                       fmt='.3f',
                       cbar_kws={'shrink': 0.8},
                       ax=ax)
            
            ax.set_title(f'{var_name}', fontsize=14, fontweight='bold')
            ax.set_xlabel('')
            ax.set_ylabel('')
            
            # 设置刻度标签
            ax.set_xticklabels(['Lianghe', 'Yantang', 'Yinhe'])
            ax.set_yticklabels(['Lianghe', 'Yantang', 'Yinhe'])
    
    # 隐藏多余的子图
    if len(variables) < len(axes):
        axes[-1].set_visible(False)
    
    plt.tight_layout()
    plt.savefig('cross_watershed_correlations.png', dpi=300, bbox_inches='tight')
    plt.show()

def generate_correlation_report(correlations, cross_corr_results, data):
    """生成相关性分析报告"""
    report = []
    report.append("# Final_data流域数据相关性分析报告\n")
    report.append(f"分析时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    # 数据概况
    report.append("## 1. 数据概况\n")
    watersheds = ['lianghe', 'yantang', 'yinhe']
    watershed_names = ['梁河流域', '雁塘流域', '银河流域']
    
    for watershed, name in zip(watersheds, watershed_names):
        df = data[watershed]
        report.append(f"### {name}\n")
        report.append(f"- 数据点数: {len(df)}\n")
        report.append(f"- 变量数: {len(df.columns)}\n")
        report.append(f"- 缺失值: {df.isnull().sum().sum()}\n")
        
        # 基本统计
        report.append("- 基本统计:\n")
        stats = df.describe()
        for var in df.columns:
            report.append(f"  - {var}: 均值={stats.loc['mean', var]:.3f}, "
                         f"标准差={stats.loc['std', var]:.3f}, "
                         f"最大值={stats.loc['max', var]:.3f}\n")
        report.append("\n")
    
    # 流域内相关性分析
    report.append("## 2. 流域内变量相关性分析\n")
    
    for watershed, name in zip(watersheds, watershed_names):
        corr_matrix = correlations[watershed]
        report.append(f"### {name}\n")
        
        # 找出最强相关性
        corr_values = []
        for i in range(len(corr_matrix.columns)):
            for j in range(i+1, len(corr_matrix.columns)):
                var1 = corr_matrix.columns[i]
                var2 = corr_matrix.columns[j]
                corr_val = corr_matrix.iloc[i, j]
                corr_values.append((var1, var2, corr_val))
        
        corr_values.sort(key=lambda x: abs(x[2]), reverse=True)
        
        report.append("**最强相关性（前5个）:**\n")
        for i, (var1, var2, corr_val) in enumerate(corr_values[:5]):
            strength = "强" if abs(corr_val) > 0.7 else "中等" if abs(corr_val) > 0.3 else "弱"
            direction = "正" if corr_val > 0 else "负"
            report.append(f"{i+1}. {var1} vs {var2}: {corr_val:.3f} ({strength}{direction}相关)\n")
        report.append("\n")
    
    # 跨流域相关性分析
    report.append("## 3. 跨流域同类变量相关性分析\n")
    
    variables = ['001', '002', '003', 'tp', 'flow']
    var_names = ['预测产品001', '预测产品002', '预测产品003', '实际降雨', '流量']
    
    for var, var_name in zip(variables, var_names):
        report.append(f"### {var_name}\n")
        cross_corr = cross_corr_results[var]
        
        # 提取非对角线元素
        cross_values = []
        for i in range(len(cross_corr.index)):
            for j in range(i+1, len(cross_corr.columns)):
                w1 = cross_corr.index[i]
                w2 = cross_corr.columns[j]
                corr_val = cross_corr.iloc[i, j]
                cross_values.append((w1, w2, corr_val))
        
        cross_values.sort(key=lambda x: abs(x[2]), reverse=True)
        
        for w1, w2, corr_val in cross_values:
            strength = "强" if abs(corr_val) > 0.7 else "中等" if abs(corr_val) > 0.3 else "弱"
            direction = "正" if corr_val > 0 else "负"
            report.append(f"- {w1} vs {w2}: {corr_val:.3f} ({strength}{direction}相关)\n")
        report.append("\n")
    
    # 主要发现
    report.append("## 4. 主要发现\n")
    report.append("### 4.1 流域内相关性特征\n")
    report.append("- 预测产品001、002、003之间的相关性\n")
    report.append("- 预测产品与实际降雨(tp)的相关性\n")
    report.append("- 降雨与流量的相关性\n")
    report.append("\n")
    
    report.append("### 4.2 跨流域相关性特征\n")
    report.append("- 不同流域间同类变量的一致性\n")
    report.append("- 流域间的差异性分析\n")
    report.append("\n")
    
    report.append("### 4.3 建议\n")
    report.append("- 基于相关性分析的模型改进建议\n")
    report.append("- 数据融合策略建议\n")
    
    # 保存报告
    with open('correlation_analysis_report.md', 'w', encoding='utf-8') as f:
        f.writelines(report)

    print("相关性分析报告已保存到: correlation_analysis_report.md")

def main():
    """主函数"""
    print("开始Final_data流域数据相关性分析...")
    
    # 1. 加载数据
    print("\n1. 加载数据...")
    data = load_watershed_data()
    
    # 2. 计算相关性
    print("\n2. 计算流域内相关性...")
    correlations = calculate_correlations(data)
    
    # 3. 绘制相关性热力图
    print("\n3. 绘制相关性热力图...")
    plot_correlation_heatmaps(correlations)
    
    # 4. 绘制散点图矩阵
    print("\n4. 绘制散点图矩阵...")
    plot_scatter_matrix(data)
    
    # 5. 跨流域相关性分析
    print("\n5. 计算跨流域相关性...")
    cross_corr_results = cross_watershed_correlation(data)
    
    # 6. 绘制跨流域相关性图
    print("\n6. 绘制跨流域相关性图...")
    plot_cross_watershed_correlations(cross_corr_results)
    
    # 7. 生成分析报告
    print("\n7. 生成分析报告...")
    generate_correlation_report(correlations, cross_corr_results, data)
    
    print("\n相关性分析完成！")
    print("生成的文件:")
    print("- correlation_heatmaps.png")
    print("- lianghe_scatter_matrix.png")
    print("- yantang_scatter_matrix.png")
    print("- yinhe_scatter_matrix.png")
    print("- cross_watershed_correlations.png")
    print("- correlation_analysis_report.md")

if __name__ == "__main__":
    main()
