import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import pearsonr

def calculate_rmse(observed, predicted):
    """Calculate Root Mean Square Error"""
    return np.sqrt(np.mean((observed - predicted) ** 2))

def calculate_nse(observed, predicted):
    """Calculate Nash-Sutcliffe Efficiency"""
    numerator = np.sum((observed - predicted) ** 2)
    denominator = np.sum((observed - np.mean(observed)) ** 2)
    if denominator == 0:
        return np.nan
    return 1 - (numerator / denominator)

def create_performance_visualization():
    """Create comprehensive performance visualization"""
    
    files = ['lianghe_final.csv', 'yantang_final.csv', 'yinhe_final.csv']
    watersheds = ['LIANGHE', 'YANTANG', 'YINHE']
    
    # Collect all metrics
    all_metrics = []
    
    for i, file in enumerate(files):
        watershed = watersheds[i]
        df = pd.read_csv(file)
        
        for product in ['001', '002', '003']:
            if product in df.columns and 'tp' in df.columns:
                observed = df['tp'].values
                predicted = df[product].values
                
                # Calculate metrics
                corr, p_val = pearsonr(predicted, observed)
                rmse = calculate_rmse(observed, predicted)
                nse = calculate_nse(observed, predicted)
                
                all_metrics.append({
                    'Watershed': watershed,
                    'Product': f'Product {product}',
                    'Correlation': corr,
                    'RMSE': rmse,
                    'NSE': nse,
                    'P_value': p_val
                })
    
    # Convert to DataFrame
    metrics_df = pd.DataFrame(all_metrics)
    
    # Create figure with subplots
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Performance Metrics Analysis: Products vs Actual Rainfall', fontsize=16, fontweight='bold')
    
    # 1. RMSE Comparison
    pivot_rmse = metrics_df.pivot(index='Watershed', columns='Product', values='RMSE')
    sns.heatmap(pivot_rmse, annot=True, fmt='.3f', cmap='Reds', ax=axes[0,0], cbar_kws={'label': 'RMSE'})
    axes[0,0].set_title('RMSE Comparison\n(Lower is Better)', fontweight='bold')
    
    # 2. NSE Comparison
    pivot_nse = metrics_df.pivot(index='Watershed', columns='Product', values='NSE')
    # Clip NSE values for better visualization (very negative values)
    pivot_nse_clipped = pivot_nse.clip(lower=-5)
    sns.heatmap(pivot_nse_clipped, annot=True, fmt='.3f', cmap='RdYlGn', center=0, ax=axes[0,1], 
                cbar_kws={'label': 'NSE (clipped at -5)'})
    axes[0,1].set_title('NSE Comparison\n(Higher is Better)', fontweight='bold')
    
    # 3. Correlation Comparison
    pivot_corr = metrics_df.pivot(index='Watershed', columns='Product', values='Correlation')
    sns.heatmap(pivot_corr, annot=True, fmt='.3f', cmap='Blues', ax=axes[0,2], cbar_kws={'label': 'Correlation'})
    axes[0,2].set_title('Correlation Comparison\n(Higher is Better)', fontweight='bold')
    
    # 4. RMSE Bar Plot
    sns.barplot(data=metrics_df, x='Watershed', y='RMSE', hue='Product', ax=axes[1,0])
    axes[1,0].set_title('RMSE by Watershed and Product', fontweight='bold')
    axes[1,0].set_ylabel('RMSE')
    axes[1,0].legend(title='Product')
    
    # 5. NSE Bar Plot (clipped for visualization)
    metrics_df_clipped = metrics_df.copy()
    metrics_df_clipped['NSE_clipped'] = metrics_df_clipped['NSE'].clip(lower=-5)
    sns.barplot(data=metrics_df_clipped, x='Watershed', y='NSE_clipped', hue='Product', ax=axes[1,1])
    axes[1,1].set_title('NSE by Watershed and Product\n(Clipped at -5)', fontweight='bold')
    axes[1,1].set_ylabel('NSE (clipped)')
    axes[1,1].axhline(y=0, color='red', linestyle='--', alpha=0.7, label='NSE=0 (baseline)')
    axes[1,1].legend(title='Product')
    
    # 6. Scatter plot: NSE vs Correlation
    colors = {'Product 001': 'red', 'Product 002': 'blue', 'Product 003': 'green'}
    for product in ['Product 001', 'Product 002', 'Product 003']:
        subset = metrics_df[metrics_df['Product'] == product]
        axes[1,2].scatter(subset['Correlation'], subset['NSE'].clip(lower=-5), 
                         label=product, color=colors[product], s=100, alpha=0.7)
        
        # Add watershed labels
        for _, row in subset.iterrows():
            axes[1,2].annotate(row['Watershed'], 
                              (row['Correlation'], max(row['NSE'], -5)), 
                              xytext=(5, 5), textcoords='offset points', fontsize=8)
    
    axes[1,2].set_xlabel('Correlation Coefficient')
    axes[1,2].set_ylabel('NSE (clipped at -5)')
    axes[1,2].set_title('NSE vs Correlation\nby Product and Watershed', fontweight='bold')
    axes[1,2].axhline(y=0, color='red', linestyle='--', alpha=0.7, label='NSE=0')
    axes[1,2].legend()
    axes[1,2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('performance_metrics_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Create a summary table
    print("\n" + "="*100)
    print("PERFORMANCE METRICS SUMMARY TABLE")
    print("="*100)
    
    # Best performing combinations
    print("\nBest Performing Combinations (by NSE):")
    best_nse = metrics_df.nlargest(3, 'NSE')
    print(best_nse[['Watershed', 'Product', 'NSE', 'RMSE', 'Correlation']].to_string(index=False))
    
    print("\nBest Performing Combinations (by RMSE - lowest):")
    best_rmse = metrics_df.nsmallest(3, 'RMSE')
    print(best_rmse[['Watershed', 'Product', 'NSE', 'RMSE', 'Correlation']].to_string(index=False))
    
    print("\nBest Performing Combinations (by Correlation - highest):")
    best_corr = metrics_df.nlargest(3, 'Correlation')
    print(best_corr[['Watershed', 'Product', 'NSE', 'RMSE', 'Correlation']].to_string(index=False))
    
    # Product ranking across all watersheds
    print(f"\n{'='*100}")
    print("PRODUCT RANKING ACROSS ALL WATERSHEDS")
    print(f"{'='*100}")
    
    product_summary = metrics_df.groupby('Product').agg({
        'NSE': ['mean', 'std'],
        'RMSE': ['mean', 'std'],
        'Correlation': ['mean', 'std']
    }).round(6)
    
    print("\nAverage Performance by Product:")
    print(product_summary)
    
    print("\nVisualization saved as: performance_metrics_analysis.png")
    
    return metrics_df

if __name__ == "__main__":
    df = create_performance_visualization()
