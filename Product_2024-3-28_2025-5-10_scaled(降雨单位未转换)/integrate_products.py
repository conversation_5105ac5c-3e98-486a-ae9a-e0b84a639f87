#!/usr/bin/env python3
"""
整合每个流域的三个产品数据到单个CSV文件
生成 lianghe.csv、yantang.csv、yinhe.csv
每个文件包含：时间、001的tp、002的tp、003的tp
"""

import pandas as pd
import os

def standardize_time_format(df):
    """
    统一时间格式为 YYYY-MM-DD HH:MM:SS
    """
    df = df.copy()

    # 检测时间列名并重命名为标准列名
    if 'time' in df.columns:
        pass  # 已经是标准列名
    elif 'valid_time' in df.columns:
        # 重命名为标准列名
        df = df.rename(columns={'valid_time': 'time'})
    else:
        raise ValueError("找不到时间列 (time 或 valid_time)")

    # 检测时间格式并转换
    sample_time = str(df['time'].iloc[0])

    if '/' in sample_time:
        # 格式如 "2024/1/1 0:00"
        df['time'] = pd.to_datetime(df['time'], format='%Y/%m/%d %H:%M')
    else:
        # 格式如 "2024-01-01 00:00:00"
        df['time'] = pd.to_datetime(df['time'])

    return df

def integrate_watershed_products(watershed_name, base_dir):
    """
    整合单个流域的三个产品数据
    """
    print(f"正在处理流域: {watershed_name}")
    
    # 读取三个产品文件
    products = {}
    for product in ['001', '002', '003']:
        file_path = os.path.join(base_dir, watershed_name, f'{product}.csv')
        if os.path.exists(file_path):
            df = pd.read_csv(file_path)
            df = standardize_time_format(df)
            products[product] = df
            print(f"  读取 {product}.csv: {len(df)} 行")
        else:
            print(f"  警告: 文件不存在 {file_path}")
            return None
    
    # 找到共同的时间范围
    time_ranges = []
    for product, df in products.items():
        time_ranges.append((df['time'].min(), df['time'].max()))
        print(f"  {product} 时间范围: {df['time'].min()} 到 {df['time'].max()}")
    
    # 计算交集时间范围
    start_time = max([tr[0] for tr in time_ranges])
    end_time = min([tr[1] for tr in time_ranges])
    
    print(f"  共同时间范围: {start_time} 到 {end_time}")
    
    # 过滤到共同时间范围
    filtered_products = {}
    for product, df in products.items():
        filtered_df = df[(df['time'] >= start_time) & (df['time'] <= end_time)].copy()
        filtered_products[product] = filtered_df
        print(f"  {product} 过滤后: {len(filtered_df)} 行")
    
    # 以001为基准进行合并
    base_df = filtered_products['001'][['time', 'tp']].copy()
    base_df = base_df.rename(columns={'tp': 'tp_001'})
    
    # 合并002
    df_002 = filtered_products['002'][['time', 'tp']].copy()
    df_002 = df_002.rename(columns={'tp': 'tp_002'})
    merged_df = pd.merge(base_df, df_002, on='time', how='outer')
    
    # 合并003
    df_003 = filtered_products['003'][['time', 'tp']].copy()
    df_003 = df_003.rename(columns={'tp': 'tp_003'})
    merged_df = pd.merge(merged_df, df_003, on='time', how='outer')
    
    # 按时间排序
    merged_df = merged_df.sort_values('time').reset_index(drop=True)
    
    # 重命名列
    merged_df.columns = ['time', '001', '002', '003']
    
    # 填充缺失值为0
    merged_df = merged_df.fillna(0)
    
    print(f"  最终合并结果: {len(merged_df)} 行")
    
    return merged_df

def main():
    """
    主函数：处理所有流域
    """
    base_dir = "Product_2024-3-28_2025-5-10_scaled"
    watersheds = ['lianghe', 'yantang', 'yinhe']
    
    if not os.path.exists(base_dir):
        print(f"错误: 目录不存在 {base_dir}")
        return
    
    for watershed in watersheds:
        print(f"\n{'='*50}")
        
        # 整合数据
        integrated_df = integrate_watershed_products(watershed, base_dir)
        
        if integrated_df is not None:
            # 保存结果
            output_file = f"{watershed}.csv"
            integrated_df.to_csv(output_file, index=False)
            print(f"  已保存: {output_file}")
            
            # 显示前几行作为验证
            print(f"  前5行预览:")
            print(integrated_df.head().to_string(index=False))
        else:
            print(f"  跳过 {watershed}：数据处理失败")
    
    print(f"\n{'='*50}")
    print("所有流域数据整合完成！")

if __name__ == "__main__":
    main()
