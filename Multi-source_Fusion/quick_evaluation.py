import pandas as pd
import numpy as np
from scipy.stats import pearsonr

def nash_sutcliffe_efficiency(observed, predicted):
    """Calculate Nash-Sutcliffe Efficiency"""
    observed = np.array(observed)
    predicted = np.array(predicted)
    
    mask = ~(np.isnan(observed) | np.isnan(predicted))
    observed = observed[mask]
    predicted = predicted[mask]
    
    if len(observed) == 0:
        return np.nan
    
    ss_res = np.sum((observed - predicted) ** 2)
    ss_tot = np.sum((observed - np.mean(observed)) ** 2)
    
    if ss_tot == 0:
        return np.nan
    
    return 1 - (ss_res / ss_tot)

def correlation_coefficient(observed, predicted):
    """Calculate Pearson correlation coefficient"""
    observed = np.array(observed)
    predicted = np.array(predicted)
    
    mask = ~(np.isnan(observed) | np.isnan(predicted))
    observed = observed[mask]
    predicted = predicted[mask]
    
    if len(observed) < 2:
        return np.nan
    
    corr, _ = pearsonr(observed, predicted)
    return corr

def root_mean_square_error(observed, predicted):
    """Calculate Root Mean Square Error"""
    observed = np.array(observed)
    predicted = np.array(predicted)
    
    mask = ~(np.isnan(observed) | np.isnan(predicted))
    observed = observed[mask]
    predicted = predicted[mask]
    
    if len(observed) == 0:
        return np.nan
    
    return np.sqrt(np.mean((observed - predicted) ** 2))

def evaluate_single_watershed(data_path, watershed_name):
    """Evaluate forecast products for a single watershed"""
    df = pd.read_csv(data_path)
    observed = df['tp'].values
    products = ['001', '002', '003']
    
    print(f"\n{watershed_name.upper()} Watershed Evaluation Results:")
    print("=" * 60)
    
    results = {}
    for product in products:
        predicted = df[product].values
        
        nse = nash_sutcliffe_efficiency(observed, predicted)
        corr = correlation_coefficient(observed, predicted)
        rmse = root_mean_square_error(observed, predicted)
        
        results[product] = {'NSE': nse, 'Correlation': corr, 'RMSE': rmse}
        
        print(f"Product {product}:")
        print(f"  NSE: {nse:.4f}")
        print(f"  Correlation: {corr:.4f}")
        print(f"  RMSE: {rmse:.4f} mm")
        print()
    
    return results

def main():
    """Main evaluation function"""
    watersheds = {
        'Lianghe': 'Multi-source_Fusion/data/lianghe_final.csv',
        'Yantang': 'Multi-source_Fusion/data/yantang_final.csv',
        'Yinhe': 'Multi-source_Fusion/data/yinhe_final.csv'
    }
    
    print("Precipitation Forecast Products Evaluation")
    print("=" * 80)
    print("Metrics: NSE (Nash-Sutcliffe Efficiency), Correlation, RMSE")
    print("Products: 001, 002, 003 vs Observed precipitation (tp)")
    
    all_results = {}
    for watershed_name, data_path in watersheds.items():
        results = evaluate_single_watershed(data_path, watershed_name)
        all_results[watershed_name] = results
    
    # Summary table
    print("\nSUMMARY TABLE:")
    print("=" * 80)
    print(f"{'Watershed':<10} {'Product':<8} {'NSE':<8} {'Corr':<8} {'RMSE':<8}")
    print("-" * 50)
    
    for watershed in all_results:
        for product in ['001', '002', '003']:
            nse = all_results[watershed][product]['NSE']
            corr = all_results[watershed][product]['Correlation']
            rmse = all_results[watershed][product]['RMSE']
            print(f"{watershed:<10} {product:<8} {nse:<8.4f} {corr:<8.4f} {rmse:<8.4f}")
    
    # Key findings
    print("\nKEY FINDINGS:")
    print("=" * 80)
    print("1. NSE Values:")
    print("   - Products 001 & 002: Close to 0 (barely acceptable)")
    print("   - Product 003: Negative values (poor performance)")
    print("\n2. Correlation:")
    print("   - Product 003: Highest correlation (0.33-0.37)")
    print("   - Products 001 & 002: Lower correlation (0.14-0.21)")
    print("\n3. RMSE:")
    print("   - Products 001 & 002: Relatively small errors")
    print("   - Product 003: Large errors, especially in Yantang")
    print("\n4. Overall Assessment:")
    print("   - All products show limited prediction capability")
    print("   - Product 003 has systematic bias issues")
    print("   - Multi-source fusion recommended for improvement")

if __name__ == "__main__":
    main()
