import csv
import math
import os

def is_nan(value):
    """检查值是否为NaN"""
    try:
        return math.isnan(float(value))
    except (ValueError, TypeError):
        return True

def remove_nan_pairs(observed, predicted):
    """移除包含NaN的数据对"""
    clean_observed = []
    clean_predicted = []

    for obs, pred in zip(observed, predicted):
        if not is_nan(obs) and not is_nan(pred):
            clean_observed.append(float(obs))
            clean_predicted.append(float(pred))

    return clean_observed, clean_predicted

def calculate_mean(data):
    """计算均值"""
    if not data:
        return float('nan')
    return sum(data) / len(data)

def calculate_std(data):
    """计算标准差"""
    if len(data) < 2:
        return float('nan')

    mean_val = calculate_mean(data)
    variance = sum((x - mean_val) ** 2 for x in data) / (len(data) - 1)
    return math.sqrt(variance)

def standardize_data(data):
    """对数据进行标准化处理 (均值-标准差标准化)"""
    if not data:
        return []

    mean_val = calculate_mean(data)
    std_val = calculate_std(data)

    if std_val == 0 or is_nan(std_val):
        return [0.0] * len(data)

    return [(x - mean_val) / std_val for x in data]

def nash_sutcliffe_efficiency(observed, predicted):
    """
    计算纳什效率系数 (NSE)
    NSE = 1 - (SS_res / SS_tot)
    其中 SS_res = Σ(observed - predicted)²
         SS_tot = Σ(observed - mean(observed))²
    """
    observed, predicted = remove_nan_pairs(observed, predicted)

    if len(observed) == 0:
        return float('nan')

    obs_mean = calculate_mean(observed)

    ss_res = sum((obs - pred) ** 2 for obs, pred in zip(observed, predicted))
    ss_tot = sum((obs - obs_mean) ** 2 for obs in observed)

    if ss_tot == 0:
        return float('nan')

    nse = 1 - (ss_res / ss_tot)
    return nse

def calculate_correlation(observed, predicted):
    """计算相关系数"""
    observed, predicted = remove_nan_pairs(observed, predicted)

    if len(observed) < 2:
        return float('nan')

    obs_mean = calculate_mean(observed)
    pred_mean = calculate_mean(predicted)

    numerator = sum((obs - obs_mean) * (pred - pred_mean) for obs, pred in zip(observed, predicted))

    obs_sq_sum = sum((obs - obs_mean) ** 2 for obs in observed)
    pred_sq_sum = sum((pred - pred_mean) ** 2 for pred in predicted)

    denominator = math.sqrt(obs_sq_sum * pred_sq_sum)

    if denominator == 0:
        return float('nan')

    return numerator / denominator

def calculate_rmse(observed, predicted):
    """计算均方根误差 (RMSE)"""
    observed, predicted = remove_nan_pairs(observed, predicted)

    if len(observed) == 0:
        return float('nan')

    mse = sum((obs - pred) ** 2 for obs, pred in zip(observed, predicted)) / len(observed)
    return math.sqrt(mse)

def read_csv_data(file_path):
    """读取CSV文件数据"""
    data = {}

    with open(file_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)

        # 初始化列表
        for fieldname in reader.fieldnames:
            data[fieldname] = []

        # 读取数据
        for row in reader:
            for fieldname in reader.fieldnames:
                try:
                    # 尝试转换为浮点数
                    value = float(row[fieldname])
                    data[fieldname].append(value)
                except (ValueError, TypeError):
                    # 如果转换失败，保持原值或设为NaN
                    data[fieldname].append(float('nan'))

    return data

def evaluate_precipitation_products():
    """
    评估三种降水预报产品与实测雨量的性能
    """
    # 数据文件路径
    data_dir = "data"
    watersheds = ["lianghe", "yantang", "yinhe"]
    products = ["001", "002", "003"]

    # 存储所有结果
    results = []

    print("开始评估降水预报产品...")
    print("=" * 60)

    for watershed in watersheds:
        print(f"\n处理流域: {watershed}")
        print("-" * 40)

        # 读取数据
        file_path = os.path.join(data_dir, f"{watershed}_final.csv")

        if not os.path.exists(file_path):
            print(f"警告: 文件 {file_path} 不存在，跳过该流域")
            continue

        try:
            data = read_csv_data(file_path)
        except Exception as e:
            print(f"警告: 读取文件 {file_path} 失败: {e}")
            continue

        # 检查必要的列是否存在
        required_cols = ["001", "002", "003", "tp"]
        missing_cols = [col for col in required_cols if col not in data.keys()]
        if missing_cols:
            print(f"警告: 缺少列 {missing_cols}，跳过该流域")
            continue

        # 获取实测雨量数据
        observed = data["tp"]

        # 对实测雨量进行标准化
        observed_std = standardize_data(observed)

        print(f"数据点数量: {len(observed)}")
        print(f"实测雨量统计: 均值={calculate_mean(observed):.4f}, 标准差={calculate_std(observed):.4f}")

        # 评估每个预报产品
        for product in products:
            print(f"\n  评估产品: {product}")

            # 获取预报数据
            predicted = data[product]

            # 对预报数据进行标准化
            predicted_std = standardize_data(predicted)

            print(f"  预报数据统计: 均值={calculate_mean(predicted):.4f}, 标准差={calculate_std(predicted):.4f}")

            # 计算评估指标（使用标准化后的数据）
            nse = nash_sutcliffe_efficiency(observed_std, predicted_std)
            correlation = calculate_correlation(observed_std, predicted_std)
            rmse = calculate_rmse(observed_std, predicted_std)

            # 同时计算原始数据的指标用于参考
            nse_raw = nash_sutcliffe_efficiency(observed, predicted)
            correlation_raw = calculate_correlation(observed, predicted)
            rmse_raw = calculate_rmse(observed, predicted)

            print(f"  标准化数据指标:")
            print(f"    NSE: {nse:.4f}")
            print(f"    相关系数: {correlation:.4f}")
            print(f"    RMSE: {rmse:.4f}")

            print(f"  原始数据指标:")
            print(f"    NSE: {nse_raw:.4f}")
            print(f"    相关系数: {correlation_raw:.4f}")
            print(f"    RMSE: {rmse_raw:.4f}")

            # 存储结果
            result = {
                "流域": watershed,
                "预报产品": product,
                "数据点数量": len(observed),
                "实测雨量均值": calculate_mean(observed),
                "实测雨量标准差": calculate_std(observed),
                "预报数据均值": calculate_mean(predicted),
                "预报数据标准差": calculate_std(predicted),
                "NSE_标准化": nse,
                "相关系数_标准化": correlation,
                "RMSE_标准化": rmse,
                "NSE_原始": nse_raw,
                "相关系数_原始": correlation_raw,
                "RMSE_原始": rmse_raw
            }

            results.append(result)

    return results

def save_results(results):
    """
    保存评估结果到CSV文件
    """
    if not results:
        print("没有结果可保存")
        return

    # 保存到CSV文件
    output_file = "precipitation_evaluation_results.csv"

    # 获取所有字段名
    fieldnames = list(results[0].keys())

    with open(output_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(results)

    print(f"\n结果已保存到: {output_file}")

    # 显示汇总统计
    print("\n" + "=" * 60)
    print("评估结果汇总")
    print("=" * 60)

    # 获取所有流域
    watersheds = list(set(result["流域"] for result in results))

    # 按流域分组显示结果
    for watershed in watersheds:
        print(f"\n流域: {watershed}")
        print("-" * 40)

        watershed_data = [result for result in results if result["流域"] == watershed]

        print("标准化数据评估指标:")
        print(f"{'产品':<8} {'NSE':<8} {'相关系数':<10} {'RMSE':<8}")
        print("-" * 36)

        for row in watershed_data:
            nse = row['NSE_标准化']
            corr = row['相关系数_标准化']
            rmse = row['RMSE_标准化']

            # 处理NaN值的显示
            nse_str = f"{nse:.4f}" if not is_nan(nse) else "NaN"
            corr_str = f"{corr:.4f}" if not is_nan(corr) else "NaN"
            rmse_str = f"{rmse:.4f}" if not is_nan(rmse) else "NaN"

            print(f"{row['预报产品']:<8} {nse_str:<8} {corr_str:<10} {rmse_str:<8}")

    # 找出最佳产品
    print("\n" + "=" * 60)
    print("最佳产品推荐 (基于标准化数据)")
    print("=" * 60)

    for watershed in watersheds:
        watershed_data = [result for result in results if result["流域"] == watershed]

        # 基于NSE找最佳产品（排除NaN）
        valid_nse = [result for result in watershed_data if not is_nan(result["NSE_标准化"])]
        if valid_nse:
            best_nse = max(valid_nse, key=lambda x: x["NSE_标准化"])
        else:
            best_nse = None

        # 基于相关系数找最佳产品（排除NaN）
        valid_corr = [result for result in watershed_data if not is_nan(result["相关系数_标准化"])]
        if valid_corr:
            best_corr = max(valid_corr, key=lambda x: x["相关系数_标准化"])
        else:
            best_corr = None

        # 基于RMSE找最佳产品（最小值，排除NaN）
        valid_rmse = [result for result in watershed_data if not is_nan(result["RMSE_标准化"])]
        if valid_rmse:
            best_rmse = min(valid_rmse, key=lambda x: x["RMSE_标准化"])
        else:
            best_rmse = None

        print(f"\n{watershed} 流域:")
        if best_nse:
            print(f"  最高NSE: 产品{best_nse['预报产品']} (NSE={best_nse['NSE_标准化']:.4f})")
        else:
            print(f"  最高NSE: 无有效数据")

        if best_corr:
            print(f"  最高相关系数: 产品{best_corr['预报产品']} (相关系数={best_corr['相关系数_标准化']:.4f})")
        else:
            print(f"  最高相关系数: 无有效数据")

        if best_rmse:
            print(f"  最低RMSE: 产品{best_rmse['预报产品']} (RMSE={best_rmse['RMSE_标准化']:.4f})")
        else:
            print(f"  最低RMSE: 无有效数据")

if __name__ == "__main__":
    # 执行评估
    results = evaluate_precipitation_products()

    # 保存结果
    save_results(results)

    print("\n评估完成！")
