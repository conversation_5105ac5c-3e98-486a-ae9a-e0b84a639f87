import csv
import matplotlib.pyplot as plt
import numpy as np
import os

def read_results_csv(file_path):
    """读取评估结果CSV文件"""
    results = []
    
    with open(file_path, 'r', encoding='utf-8-sig') as file:
        reader = csv.DictReader(file)
        for row in reader:
            # 转换数值字段
            for key in row:
                if key not in ['流域', '预报产品']:
                    try:
                        row[key] = float(row[key])
                    except (ValueError, TypeError):
                        row[key] = float('nan')
            results.append(row)
    
    return results

def create_evaluation_plots(results):
    """创建评估指标的可视化图表"""
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 准备数据
    watersheds = ['lianghe', 'yantang', 'yinhe']
    products = ['001', '002', '003']
    
    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('降水预报产品评估结果', fontsize=16, fontweight='bold')
    
    # 1. NSE对比图
    ax1 = axes[0, 0]
    nse_data = []
    for watershed in watersheds:
        watershed_nse = []
        for product in products:
            result = next((r for r in results if r['流域'] == watershed and r['预报产品'] == product), None)
            if result:
                watershed_nse.append(result['NSE_标准化'])
            else:
                watershed_nse.append(0)
        nse_data.append(watershed_nse)
    
    x = np.arange(len(products))
    width = 0.25
    
    for i, watershed in enumerate(watersheds):
        ax1.bar(x + i * width, nse_data[i], width, label=f'{watershed}流域')
    
    ax1.set_xlabel('预报产品')
    ax1.set_ylabel('NSE (标准化数据)')
    ax1.set_title('纳什效率系数对比')
    ax1.set_xticks(x + width)
    ax1.set_xticklabels(products)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 相关系数对比图
    ax2 = axes[0, 1]
    corr_data = []
    for watershed in watersheds:
        watershed_corr = []
        for product in products:
            result = next((r for r in results if r['流域'] == watershed and r['预报产品'] == product), None)
            if result:
                watershed_corr.append(result['相关系数_标准化'])
            else:
                watershed_corr.append(0)
        corr_data.append(watershed_corr)
    
    for i, watershed in enumerate(watersheds):
        ax2.bar(x + i * width, corr_data[i], width, label=f'{watershed}流域')
    
    ax2.set_xlabel('预报产品')
    ax2.set_ylabel('相关系数 (标准化数据)')
    ax2.set_title('相关系数对比')
    ax2.set_xticks(x + width)
    ax2.set_xticklabels(products)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. RMSE对比图
    ax3 = axes[1, 0]
    rmse_data = []
    for watershed in watersheds:
        watershed_rmse = []
        for product in products:
            result = next((r for r in results if r['流域'] == watershed and r['预报产品'] == product), None)
            if result:
                watershed_rmse.append(result['RMSE_标准化'])
            else:
                watershed_rmse.append(0)
        rmse_data.append(watershed_rmse)
    
    for i, watershed in enumerate(watersheds):
        ax3.bar(x + i * width, rmse_data[i], width, label=f'{watershed}流域')
    
    ax3.set_xlabel('预报产品')
    ax3.set_ylabel('RMSE (标准化数据)')
    ax3.set_title('均方根误差对比')
    ax3.set_xticks(x + width)
    ax3.set_xticklabels(products)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 综合评估雷达图
    ax4 = axes[1, 1]
    
    # 为每个流域创建雷达图数据
    angles = np.linspace(0, 2 * np.pi, len(products), endpoint=False).tolist()
    angles += angles[:1]  # 闭合图形
    
    colors = ['red', 'blue', 'green']
    
    for i, watershed in enumerate(watersheds):
        # 计算综合得分 (NSE + 相关系数 - RMSE/2)
        scores = []
        for product in products:
            result = next((r for r in results if r['流域'] == watershed and r['预报产品'] == product), None)
            if result:
                score = result['NSE_标准化'] + result['相关系数_标准化'] - result['RMSE_标准化']/2
                scores.append(score)
            else:
                scores.append(0)
        
        scores += scores[:1]  # 闭合图形
        
        ax4.plot(angles, scores, 'o-', linewidth=2, label=f'{watershed}流域', color=colors[i])
        ax4.fill(angles, scores, alpha=0.25, color=colors[i])
    
    ax4.set_xticks(angles[:-1])
    ax4.set_xticklabels(products)
    ax4.set_title('综合评估得分\n(NSE + 相关系数 - RMSE/2)')
    ax4.legend()
    ax4.grid(True)
    
    plt.tight_layout()
    plt.savefig('precipitation_evaluation_plots.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("评估图表已保存为: precipitation_evaluation_plots.png")

def generate_markdown_report(results):
    """生成Markdown格式的评估报告"""
    
    report = """# 降水预报产品评估报告

## 概述

本报告对三个流域（梁河、雁塘、银河）的三种降水预报产品（001、002、003）与实测雨量进行了对比评估。评估采用了标准化处理（均值-标准差标准化），并计算了以下指标：

- **纳什效率系数 (NSE)**: 衡量模型预测精度，值越接近1表示预测越准确
- **相关系数**: 衡量预测值与观测值的线性相关程度，值越接近1表示相关性越强
- **均方根误差 (RMSE)**: 衡量预测误差，值越小表示误差越小

## 评估结果

### 标准化数据评估指标

"""
    
    # 按流域分组显示结果
    watersheds = ['lianghe', 'yantang', 'yinhe']
    watershed_names = {'lianghe': '梁河', 'yantang': '雁塘', 'yinhe': '银河'}
    
    for watershed in watersheds:
        report += f"#### {watershed_names[watershed]}流域\n\n"
        report += "| 预报产品 | NSE | 相关系数 | RMSE |\n"
        report += "|---------|-----|---------|------|\n"
        
        watershed_results = [r for r in results if r['流域'] == watershed]
        for result in watershed_results:
            nse = f"{result['NSE_标准化']:.4f}"
            corr = f"{result['相关系数_标准化']:.4f}"
            rmse = f"{result['RMSE_标准化']:.4f}"
            report += f"| {result['预报产品']} | {nse} | {corr} | {rmse} |\n"
        
        report += "\n"
    
    # 最佳产品推荐
    report += "## 最佳产品推荐\n\n"
    
    for watershed in watersheds:
        watershed_data = [r for r in results if r['流域'] == watershed]
        
        # 找最佳产品
        best_nse = max(watershed_data, key=lambda x: x['NSE_标准化'])
        best_corr = max(watershed_data, key=lambda x: x['相关系数_标准化'])
        best_rmse = min(watershed_data, key=lambda x: x['RMSE_标准化'])
        
        report += f"### {watershed_names[watershed]}流域\n\n"
        report += f"- **最高NSE**: 产品{best_nse['预报产品']} (NSE={best_nse['NSE_标准化']:.4f})\n"
        report += f"- **最高相关系数**: 产品{best_corr['预报产品']} (相关系数={best_corr['相关系数_标准化']:.4f})\n"
        report += f"- **最低RMSE**: 产品{best_rmse['预报产品']} (RMSE={best_rmse['RMSE_标准化']:.4f})\n\n"
    
    # 总结
    report += """## 总结

根据评估结果，可以得出以下结论：

1. **产品003在所有流域都表现最佳**：在NSE、相关系数和RMSE三个指标上，产品003在所有三个流域都优于产品001和002。

2. **所有产品的NSE值都为负数**：这表明所有预报产品的性能都不如简单的均值预测，说明降水预报仍然存在较大挑战。

3. **产品003的相关系数最高**：产品003与实测雨量的相关性最强，在梁河流域达到0.3745，在银河流域达到0.3398。

4. **RMSE方面产品003表现最佳**：产品003的预测误差最小，在所有流域都显著优于其他两个产品。

## 建议

1. **优先使用产品003**：基于当前评估结果，建议在所有流域都优先使用产品003进行降水预报。

2. **继续改进预报模型**：由于所有产品的NSE值都为负，建议继续改进预报算法和模型。

3. **考虑集成预报**：可以考虑将多个产品进行集成，可能会获得更好的预报效果。

4. **增加更多评估指标**：建议增加更多评估指标，如偏差、技能评分等，以更全面地评估预报性能。

---

*报告生成时间: 自动生成*
*数据来源: 三个流域的降水预报产品与实测雨量对比*
"""
    
    # 保存报告
    with open('降水预报产品评估报告.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("评估报告已保存为: 降水预报产品评估报告.md")

if __name__ == "__main__":
    # 读取评估结果
    results_file = "precipitation_evaluation_results.csv"
    
    if not os.path.exists(results_file):
        print(f"错误: 找不到结果文件 {results_file}")
        print("请先运行 precipitation_evaluation.py 生成评估结果")
        exit(1)
    
    results = read_results_csv(results_file)
    
    # 生成图表
    try:
        create_evaluation_plots(results)
    except ImportError:
        print("警告: matplotlib未安装，跳过图表生成")
    except Exception as e:
        print(f"警告: 图表生成失败: {e}")
    
    # 生成报告
    generate_markdown_report(results)
    
    print("\n报告生成完成！")
