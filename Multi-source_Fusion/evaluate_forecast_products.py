import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import pearsonr
import warnings
warnings.filterwarnings('ignore')

def nash_sutcliffe_efficiency(observed, predicted):
    """
    计算纳什效率系数 (Nash-Sutcliffe Efficiency, NSE)
    NSE = 1 - (SS_res / SS_tot)
    其中 SS_res = Σ(observed - predicted)²
         SS_tot = Σ(observed - mean(observed))²
    
    NSE范围: (-∞, 1]
    NSE = 1: 完美预测
    NSE = 0: 预测效果等同于使用观测值的平均值
    NSE < 0: 预测效果比使用观测值平均值还差
    """
    observed = np.array(observed)
    predicted = np.array(predicted)
    
    # 移除包含NaN的数据点
    mask = ~(np.isnan(observed) | np.isnan(predicted))
    observed = observed[mask]
    predicted = predicted[mask]
    
    if len(observed) == 0:
        return np.nan
    
    ss_res = np.sum((observed - predicted) ** 2)
    ss_tot = np.sum((observed - np.mean(observed)) ** 2)
    
    if ss_tot == 0:
        return np.nan
    
    nse = 1 - (ss_res / ss_tot)
    return nse

def correlation_coefficient(observed, predicted):
    """
    计算皮尔逊相关系数
    """
    observed = np.array(observed)
    predicted = np.array(predicted)
    
    # 移除包含NaN的数据点
    mask = ~(np.isnan(observed) | np.isnan(predicted))
    observed = observed[mask]
    predicted = predicted[mask]
    
    if len(observed) < 2:
        return np.nan, np.nan
    
    corr, p_value = pearsonr(observed, predicted)
    return corr, p_value

def root_mean_square_error(observed, predicted):
    """
    计算均方根误差 (Root Mean Square Error, RMSE)
    RMSE = sqrt(mean((observed - predicted)²))
    """
    observed = np.array(observed)
    predicted = np.array(predicted)
    
    # 移除包含NaN的数据点
    mask = ~(np.isnan(observed) | np.isnan(predicted))
    observed = observed[mask]
    predicted = predicted[mask]
    
    if len(observed) == 0:
        return np.nan
    
    rmse = np.sqrt(np.mean((observed - predicted) ** 2))
    return rmse

def evaluate_forecast_products(data_path, watershed_name):
    """
    评估三种降水预报产品的性能
    """
    # 读取数据
    df = pd.read_csv(data_path)
    
    # 确保时间列为datetime格式
    df['time'] = pd.to_datetime(df['time'])
    
    # 提取预报产品和实测数据
    products = ['001', '002', '003']
    observed = df['tp'].values
    
    results = {}
    
    print(f"\n=== {watershed_name} 流域降水预报产品评估结果 ===")
    print(f"数据时间范围: {df['time'].min()} 至 {df['time'].max()}")
    print(f"总数据点数: {len(df)}")
    
    # 计算基本统计信息
    print(f"\n实测降水量统计:")
    print(f"  平均值: {np.nanmean(observed):.3f} mm")
    print(f"  标准差: {np.nanstd(observed):.3f} mm")
    print(f"  最大值: {np.nanmax(observed):.3f} mm")
    print(f"  最小值: {np.nanmin(observed):.3f} mm")
    print(f"  非零值数量: {np.sum(observed > 0)}")
    
    for product in products:
        predicted = df[product].values
        
        # 计算评估指标
        nse = nash_sutcliffe_efficiency(observed, predicted)
        corr, p_value = correlation_coefficient(observed, predicted)
        rmse = root_mean_square_error(observed, predicted)
        
        # 计算基本统计信息
        mean_pred = np.nanmean(predicted)
        std_pred = np.nanstd(predicted)
        max_pred = np.nanmax(predicted)
        min_pred = np.nanmin(predicted)
        nonzero_pred = np.sum(predicted > 0)
        
        results[product] = {
            'NSE': nse,
            'Correlation': corr,
            'P_value': p_value,
            'RMSE': rmse,
            'Mean': mean_pred,
            'Std': std_pred,
            'Max': max_pred,
            'Min': min_pred,
            'NonZero': nonzero_pred
        }
        
        print(f"\n产品 {product} 评估结果:")
        print(f"  纳什效率系数 (NSE): {nse:.4f}")
        print(f"  相关系数 (r): {corr:.4f} (p-value: {p_value:.4e})")
        print(f"  均方根误差 (RMSE): {rmse:.4f} mm")
        print(f"  预报降水量统计:")
        print(f"    平均值: {mean_pred:.3f} mm")
        print(f"    标准差: {std_pred:.3f} mm")
        print(f"    最大值: {max_pred:.3f} mm")
        print(f"    最小值: {min_pred:.3f} mm")
        print(f"    非零值数量: {nonzero_pred}")
    
    return results, df

def create_evaluation_plots(df, watershed_name, save_path=None):
    """
    创建评估图表
    """
    products = ['001', '002', '003']
    observed = df['tp'].values
    
    # 设置图表样式
    plt.style.use('default')
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle(f'{watershed_name} Watershed - Precipitation Forecast Products Evaluation', 
                 fontsize=16, fontweight='bold')
    
    # 散点图 - 预测值 vs 观测值
    for i, product in enumerate(products):
        predicted = df[product].values
        
        # 移除NaN值
        mask = ~(np.isnan(observed) | np.isnan(predicted))
        obs_clean = observed[mask]
        pred_clean = predicted[mask]
        
        ax = axes[0, i]
        ax.scatter(obs_clean, pred_clean, alpha=0.6, s=20)
        
        # 添加1:1线
        max_val = max(np.max(obs_clean), np.max(pred_clean))
        ax.plot([0, max_val], [0, max_val], 'r--', linewidth=2, label='1:1 Line')
        
        ax.set_xlabel('Observed Precipitation (mm)')
        ax.set_ylabel('Predicted Precipitation (mm)')
        ax.set_title(f'Product {product}')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    # 时间序列图 - 选择一个月的数据进行展示
    start_date = '2024-04-01'
    end_date = '2024-04-30'
    df_subset = df[(df['time'] >= start_date) & (df['time'] <= end_date)]
    
    for i, product in enumerate(products):
        ax = axes[1, i]
        ax.plot(df_subset['time'], df_subset['tp'], 'k-', linewidth=2, label='Observed', alpha=0.8)
        ax.plot(df_subset['time'], df_subset[product], 'b-', linewidth=1.5, label=f'Product {product}', alpha=0.7)
        
        ax.set_xlabel('Time')
        ax.set_ylabel('Precipitation (mm)')
        ax.set_title(f'Time Series - Product {product} (April 2024)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 旋转x轴标签
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存至: {save_path}")
    
    plt.show()

def create_summary_plots(summary_df):
    """
    创建汇总评估图表
    """
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle('Precipitation Forecast Products Performance Summary', fontsize=16, fontweight='bold')

    metrics = ['NSE', 'Correlation', 'RMSE']
    metric_names = ['Nash-Sutcliffe Efficiency', 'Correlation Coefficient', 'Root Mean Square Error (mm)']

    for i, (metric, metric_name) in enumerate(zip(metrics, metric_names)):
        ax = axes[i]

        # 创建分组柱状图
        watersheds = summary_df['Watershed'].unique()
        products = ['001', '002', '003']

        x = np.arange(len(watersheds))
        width = 0.25

        for j, product in enumerate(products):
            values = []
            for watershed in watersheds:
                value = summary_df[(summary_df['Watershed'] == watershed) &
                                 (summary_df['Product'] == product)][metric].iloc[0]
                values.append(value)

            ax.bar(x + j*width, values, width, label=f'Product {product}', alpha=0.8)

        ax.set_xlabel('Watershed')
        ax.set_ylabel(metric_name)
        ax.set_title(f'{metric_name} by Watershed and Product')
        ax.set_xticks(x + width)
        ax.set_xticklabels([w.capitalize() for w in watersheds])
        ax.legend()
        ax.grid(True, alpha=0.3)

        # 为NSE添加参考线
        if metric == 'NSE':
            ax.axhline(y=0, color='red', linestyle='--', alpha=0.7, label='NSE = 0')
            ax.axhline(y=0.5, color='orange', linestyle='--', alpha=0.7, label='NSE = 0.5')

    plt.tight_layout()
    plt.savefig('Multi-source_Fusion/forecast_evaluation_summary.png', dpi=300, bbox_inches='tight')
    print("汇总图表已保存至: Multi-source_Fusion/forecast_evaluation_summary.png")
    plt.show()

def main():
    """
    主函数 - 评估三个流域的降水预报产品
    """
    watersheds = {
        'lianghe': 'Multi-source_Fusion/data/lianghe_final.csv',
        'yantang': 'Multi-source_Fusion/data/yantang_final.csv',
        'yinhe': 'Multi-source_Fusion/data/yinhe_final.csv'
    }

    all_results = {}

    for watershed_name, data_path in watersheds.items():
        print(f"\n{'='*60}")
        print(f"正在评估 {watershed_name.upper()} 流域")
        print(f"{'='*60}")

        try:
            results, df = evaluate_forecast_products(data_path, watershed_name)
            all_results[watershed_name] = results

            # 创建评估图表
            plot_save_path = f'Multi-source_Fusion/{watershed_name}_evaluation_plots.png'
            create_evaluation_plots(df, watershed_name.capitalize(), plot_save_path)

        except Exception as e:
            print(f"处理 {watershed_name} 流域数据时出错: {str(e)}")
            continue

    # 创建汇总表格
    print(f"\n{'='*80}")
    print("所有流域评估结果汇总")
    print(f"{'='*80}")

    # 创建汇总DataFrame
    summary_data = []
    for watershed in all_results:
        for product in ['001', '002', '003']:
            if product in all_results[watershed]:
                row = {
                    'Watershed': watershed,
                    'Product': product,
                    'NSE': all_results[watershed][product]['NSE'],
                    'Correlation': all_results[watershed][product]['Correlation'],
                    'RMSE': all_results[watershed][product]['RMSE'],
                    'Mean_Predicted': all_results[watershed][product]['Mean'],
                    'Std_Predicted': all_results[watershed][product]['Std']
                }
                summary_data.append(row)

    summary_df = pd.DataFrame(summary_data)

    # 保存汇总结果
    summary_df.to_csv('Multi-source_Fusion/forecast_evaluation_summary.csv', index=False)
    print("汇总结果已保存至: Multi-source_Fusion/forecast_evaluation_summary.csv")

    # 显示汇总表格
    print("\n汇总表格:")
    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', None)
    pd.set_option('display.precision', 4)
    print(summary_df.to_string(index=False))

    # 创建汇总图表
    create_summary_plots(summary_df)

    return all_results

if __name__ == "__main__":
    all_results = main()
